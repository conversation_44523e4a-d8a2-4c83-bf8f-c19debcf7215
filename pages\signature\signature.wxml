<!--pages/signature/signature.wxml-->
<view class="signature-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="title">电子签名管理</view>
    <view class="subtitle">用于交班确认的电子签名</view>
  </view>

  <!-- 当前签名展示 -->
  <view class="current-signature" wx:if="{{hasSignature}}">
    <view class="section-title">当前签名</view>
    <view class="signature-preview">
      <image class="signature-image" src="{{currentSignature}}" mode="aspectFit"></image>
      <view class="signature-info">
        <text class="create-time">创建时间：{{signatureCreateTime}}</text>
        <text class="usage-count">使用次数：{{signatureUsageCount}}次</text>
      </view>
    </view>
    <view class="signature-actions">
      <van-button type="default" size="small" bindtap="previewSignature">预览</van-button>
      <van-button type="primary" size="small" bindtap="updateSignature">更新签名</van-button>
      <van-button type="danger" size="small" bindtap="deleteSignature">删除签名</van-button>
    </view>
  </view>

  <!-- 创建签名提示 -->
  <view class="no-signature" wx:if="{{!hasSignature}}">
    <van-empty description="您还没有设置电子签名">
      <van-button type="primary" size="large" bindtap="createSignature">创建签名</van-button>
    </van-empty>
  </view>

  <!-- 签名画板 -->
  <view class="signature-board" wx:if="{{showSignatureBoard}}">
    <view class="board-header">
      <view class="board-title">{{isUpdate ? '更新' : '创建'}}电子签名</view>
      <view class="board-tips">请在下方区域内签名</view>
    </view>
    
    <view class="canvas-container">
      <canvas 
        class="signature-canvas" 
        canvas-id="signatureCanvas"
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove"
        bindtouchend="onTouchEnd"
        disable-scroll="true">
      </canvas>
      
      <!-- 画板工具栏 -->
      <view class="canvas-toolbar">
        <view class="tool-group">
          <text class="tool-label">画笔粗细：</text>
          <van-slider 
            value="{{brushSize}}" 
            min="2" 
            max="10" 
            step="1"
            bind:change="onBrushSizeChange"
            custom-style="width: 200rpx;"
          />
          <text class="tool-value">{{brushSize}}px</text>
        </view>
        <view class="tool-group">
          <text class="tool-label">画笔颜色：</text>
          <view class="color-picker">
            <view 
              class="color-item {{brushColor === '#000000' ? 'active' : ''}}"
              style="background-color: #000000;"
              bindtap="selectColor"
              data-color="#000000">
            </view>
            <view 
              class="color-item {{brushColor === '#1989fa' ? 'active' : ''}}"
              style="background-color: #1989fa;"
              bindtap="selectColor"
              data-color="#1989fa">
            </view>
            <view 
              class="color-item {{brushColor === '#07c160' ? 'active' : ''}}"
              style="background-color: #07c160;"
              bindtap="selectColor"
              data-color="#07c160">
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="board-actions">
      <van-button type="default" bindtap="clearCanvas">清空</van-button>
      <van-button type="default" bindtap="cancelSignature">取消</van-button>
      <van-button 
        type="primary" 
        bindtap="saveSignature"
        loading="{{saveLoading}}"
        disabled="{{!hasDrawn || saveLoading}}">
        保存签名
      </van-button>
    </view>
  </view>

  <!-- 签名使用说明 -->
  <view class="signature-guide" wx:if="{{!showSignatureBoard}}">
    <view class="section-title">使用说明</view>
    <view class="guide-content">
      <view class="guide-item">
        <van-icon name="info-o" size="16px" color="#1989fa" />
        <text>电子签名用于交班确认，具有法律效力</text>
      </view>
      <view class="guide-item">
        <van-icon name="info-o" size="16px" color="#1989fa" />
        <text>建议使用真实姓名的手写签名</text>
      </view>
      <view class="guide-item">
        <van-icon name="info-o" size="16px" color="#1989fa" />
        <text>签名图片会加密存储，确保安全性</text>
      </view>
      <view class="guide-item">
        <van-icon name="info-o" size="16px" color="#1989fa" />
        <text>每次使用签名都会记录操作日志</text>
      </view>
    </view>
  </view>

  <!-- 签名历史 -->
  <view class="signature-history" wx:if="{{hasSignature && !showSignatureBoard}}">
    <view class="section-title">使用记录</view>
    <view class="history-list">
      <view class="history-item" wx:for="{{signatureHistory}}" wx:key="id">
        <view class="history-info">
          <view class="history-title">{{item.title}}</view>
          <view class="history-time">{{item.time}}</view>
        </view>
        <view class="history-status">
          <van-tag type="{{item.status === 'success' ? 'success' : 'warning'}}" size="medium">
            {{item.statusText}}
          </van-tag>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMoreHistory}}">
      <van-button type="default" size="small" bindtap="loadMoreHistory" loading="{{loadingHistory}}">
        加载更多
      </van-button>
    </view>
  </view>
</view>

<!-- 预览弹窗 -->
<van-popup 
  show="{{showPreview}}" 
  position="center" 
  round
  bind:close="closePreview"
  custom-style="width: 80%; max-width: 600rpx;">
  <view class="preview-popup">
    <view class="preview-header">
      <text class="preview-title">签名预览</text>
      <van-icon name="cross" bindtap="closePreview" />
    </view>
    <view class="preview-content">
      <image class="preview-image" src="{{currentSignature}}" mode="aspectFit"></image>
    </view>
  </view>
</van-popup>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Dialog组件 -->
<van-dialog id="van-dialog" />
