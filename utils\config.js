// 系统配置文件
const config = {
  // 云开发环境配置
  cloud: {
    env: 'cssd-shift-system', // 云开发环境ID
    timeout: 10000, // 请求超时时间
    retryTimes: 3 // 重试次数
  },

  // 数据库集合名称
  collections: {
    users: 'users',
    shiftRecords: 'shift_records',
    handoverLogs: 'handover_logs',
    instruments: 'instruments',
    departments: 'departments',
    notifications: 'notifications',
    systemLogs: 'system_logs'
  },

  // 云函数名称
  functions: {
    login: 'login',
    getUserInfo: 'getUserInfo',
    createShift: 'createShift',
    updateShift: 'updateShift',
    getShiftList: 'getShiftList',
    confirmHandover: 'confirmHandover',
    sendNotification: 'sendNotification',
    uploadFile: 'uploadFile',
    reportError: 'reportError',
    getStatistics: 'getStatistics'
  },

  // 用户角色配置
  roles: {
    NURSE: 'nurse',
    HEAD_NURSE: 'head_nurse',
    ADMIN: 'admin'
  },

  // 班次类型
  shiftTypes: [
    { value: 'morning', label: '早班', time: '08:00-16:00' },
    { value: 'afternoon', label: '中班', time: '16:00-24:00' },
    { value: 'night', label: '夜班', time: '00:00-08:00' },
    { value: 'holiday', label: '节假日班', time: '全天' }
  ],

  // 器械分类
  instrumentCategories: [
    {
      id: 'surgical',
      name: '手术器械',
      items: [
        { name: '腹腔镜', code: 'FQJ', unit: '套' },
        { name: '持针器', code: 'CZQ', unit: '把' },
        { name: '血管钳', code: 'XGQ', unit: '把' },
        { name: '组织钳', code: 'ZZQ', unit: '把' },
        { name: '手术剪', code: 'SSJ', unit: '把' }
      ]
    },
    {
      id: 'respiratory',
      name: '呼吸管路',
      items: [
        { name: '呼吸机管路', code: 'HXJGL', unit: '套' },
        { name: '雾化器', code: 'WHQ', unit: '个' },
        { name: '氧气面罩', code: 'YQMZ', unit: '个' }
      ]
    },
    {
      id: 'monitoring',
      name: '监护设备',
      items: [
        { name: '心电监护仪', code: 'XDJHY', unit: '台' },
        { name: '血压计', code: 'XYJ', unit: '台' },
        { name: '体温计', code: 'TWJ', unit: '支' }
      ]
    }
  ],

  // 状态配置
  status: {
    DRAFT: 'draft',
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    REJECTED: 'rejected',
    ARCHIVED: 'archived'
  },

  // 状态显示配置
  statusDisplay: {
    draft: { text: '草稿', color: '#969799' },
    pending: { text: '待接收', color: '#ff976a' },
    confirmed: { text: '已确认', color: '#07c160' },
    rejected: { text: '已退回', color: '#ee0a24' },
    archived: { text: '已归档', color: '#646566' }
  },

  // 优先级配置
  priority: {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
  },

  // 优先级显示配置
  priorityDisplay: {
    low: { text: '低', color: '#07c160' },
    normal: { text: '普通', color: '#1989fa' },
    high: { text: '高', color: '#ff976a' },
    urgent: { text: '紧急', color: '#ee0a24' }
  },

  // 消息类型
  messageTypes: {
    SHIFT_CREATED: 'shift_created',
    SHIFT_CONFIRMED: 'shift_confirmed',
    SHIFT_REJECTED: 'shift_rejected',
    HANDOVER_REQUEST: 'handover_request',
    SYSTEM_NOTICE: 'system_notice',
    URGENT_NOTICE: 'urgent_notice'
  },

  // 文件上传配置
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    cloudPath: 'uploads/'
  },

  // 缓存配置
  cache: {
    userInfo: 'cache_user_info',
    shiftList: 'cache_shift_list',
    instrumentList: 'cache_instrument_list',
    expireTime: 30 * 60 * 1000 // 30分钟
  },

  // 页面路径配置
  pages: {
    index: '/pages/index/index',
    login: '/pages/login/login',
    shiftCreate: '/pages/shift/create/create',
    shiftList: '/pages/shift/list/list',
    shiftDetail: '/pages/shift/detail/detail',
    handoverScan: '/pages/handover/scan/scan',
    handoverConfirm: '/pages/handover/confirm/confirm',
    message: '/pages/message/message',
    dashboard: '/pages/dashboard/dashboard',
    profile: '/pages/profile/profile',
    signature: '/pages/signature/signature'
  },

  // API配置
  api: {
    baseUrl: 'https://api.example.com',
    timeout: 10000,
    retryTimes: 3
  },

  // 开发环境配置
  dev: {
    enableLog: true,
    enableMock: false,
    mockDelay: 1000
  }
}

module.exports = config
