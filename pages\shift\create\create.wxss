/* pages/shift/create/create.wxss */
.create-container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding: 20rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #969799;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20rpx;
}

/* 器械清点 */
.instrument-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.instrument-item {
  border: 2rpx solid #ebedf0;
  border-radius: 12rpx;
  padding: 20rpx;
  background: #f7f8fa;
}

.instrument-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.instrument-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #323233;
}

.instrument-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.label {
  font-size: 26rpx;
  color: #646566;
  min-width: 120rpx;
}

.unit {
  font-size: 26rpx;
  color: #969799;
  margin-left: 8rpx;
}

.instrument-status {
  margin-top: 16rpx;
  text-align: right;
}

/* 设备状态 */
.equipment-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.equipment-item {
  border: 2rpx solid #ebedf0;
  border-radius: 12rpx;
  padding: 20rpx;
  background: #f7f8fa;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.equipment-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #323233;
}

.equipment-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 附件上传 */
.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  gap: 16rpx;
}

.attachment-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.attachment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.attachment-name {
  font-size: 28rpx;
  color: #323233;
}

.attachment-size {
  font-size: 24rpx;
  color: #969799;
}

/* 提交按钮 */
.submit-section {
  margin: 40rpx 0;
}

/* 弹窗样式 */
.instrument-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.instrument-search {
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.instrument-options {
  flex: 1;
  padding: 20rpx 30rpx;
  overflow-y: auto;
}

.instrument-option {
  padding: 20rpx;
  border-bottom: 2rpx solid #ebedf0;
  cursor: pointer;
}

.instrument-option:last-child {
  border-bottom: none;
}

.option-name {
  font-size: 30rpx;
  color: #323233;
  margin-bottom: 8rpx;
}

.option-info {
  display: flex;
  gap: 20rpx;
}

.option-code,
.option-unit {
  font-size: 24rpx;
  color: #969799;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .detail-row {
    flex-wrap: wrap;
    gap: 8rpx;
  }
  
  .label {
    min-width: 100rpx;
  }
}

/* 安全区域适配 */
.create-container {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
