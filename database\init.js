// 数据库初始化脚本
// 在微信开发者工具的云开发控制台中运行此脚本

// 1. 创建用户集合
db.createCollection('users')

// 2. 创建交班记录集合
db.createCollection('shift_records')

// 3. 创建交接日志集合
db.createCollection('handover_logs')

// 4. 创建器械信息集合
db.createCollection('instruments')

// 5. 创建部门信息集合
db.createCollection('departments')

// 6. 创建通知消息集合
db.createCollection('notifications')

// 7. 创建系统日志集合
db.createCollection('system_logs')

// 初始化器械数据
const instruments = [
  // 手术器械
  { category: 'surgical', name: '腹腔镜', code: 'FQJ', unit: '套', status: 'active' },
  { category: 'surgical', name: '持针器', code: 'CZQ', unit: '把', status: 'active' },
  { category: 'surgical', name: '血管钳', code: 'XGQ', unit: '把', status: 'active' },
  { category: 'surgical', name: '组织钳', code: 'ZZQ', unit: '把', status: 'active' },
  { category: 'surgical', name: '手术剪', code: 'SSJ', unit: '把', status: 'active' },
  
  // 呼吸管路
  { category: 'respiratory', name: '呼吸机管路', code: 'HXJGL', unit: '套', status: 'active' },
  { category: 'respiratory', name: '雾化器', code: 'WHQ', unit: '个', status: 'active' },
  { category: 'respiratory', name: '氧气面罩', code: 'YQMZ', unit: '个', status: 'active' },
  
  // 监护设备
  { category: 'monitoring', name: '心电监护仪', code: 'XDJHY', unit: '台', status: 'active' },
  { category: 'monitoring', name: '血压计', code: 'XYJ', unit: '台', status: 'active' },
  { category: 'monitoring', name: '体温计', code: 'TWJ', unit: '支', status: 'active' }
]

// 批量插入器械数据
instruments.forEach(instrument => {
  db.collection('instruments').add({
    data: {
      ...instrument,
      createTime: new Date(),
      updateTime: new Date()
    }
  })
})

// 初始化部门数据
const departments = [
  { name: '手术器械组', code: 'SSQX', description: '负责手术器械的清洗、消毒、包装' },
  { name: '呼吸管路组', code: 'HXGL', description: '负责呼吸相关器械的处理' },
  { name: '监护设备组', code: 'JHSB', description: '负责监护设备的维护保养' },
  { name: '质控组', code: 'ZK', description: '负责质量控制和检查' }
]

// 批量插入部门数据
departments.forEach(department => {
  db.collection('departments').add({
    data: {
      ...department,
      status: 'active',
      createTime: new Date(),
      updateTime: new Date()
    }
  })
})

// 创建索引
// 用户集合索引
db.collection('users').createIndex({
  keys: { openid: 1 },
  options: { unique: true, name: 'openid_unique' }
})

db.collection('users').createIndex({
  keys: { phone: 1 },
  options: { sparse: true, name: 'phone_index' }
})

// 交班记录集合索引
db.collection('shift_records').createIndex({
  keys: { shiftId: 1 },
  options: { unique: true, name: 'shiftId_unique' }
})

db.collection('shift_records').createIndex({
  keys: { creator: 1, createTime: -1 },
  options: { name: 'creator_createTime_index' }
})

db.collection('shift_records').createIndex({
  keys: { status: 1, createTime: -1 },
  options: { name: 'status_createTime_index' }
})

// 交接日志集合索引
db.collection('handover_logs').createIndex({
  keys: { recordId: 1, timestamp: -1 },
  options: { name: 'recordId_timestamp_index' }
})

// 通知消息集合索引
db.collection('notifications').createIndex({
  keys: { userId: 1, createTime: -1 },
  options: { name: 'userId_createTime_index' }
})

db.collection('notifications').createIndex({
  keys: { userId: 1, isRead: 1 },
  options: { name: 'userId_isRead_index' }
})

// 系统日志集合索引
db.collection('system_logs').createIndex({
  keys: { userId: 1, timestamp: -1 },
  options: { name: 'userId_timestamp_index' }
})

db.collection('system_logs').createIndex({
  keys: { type: 1, timestamp: -1 },
  options: { name: 'type_timestamp_index' }
})

console.log('数据库初始化完成！')
