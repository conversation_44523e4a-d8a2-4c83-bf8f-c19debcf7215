// 云函数：更新用户信息
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { userId, updateData } = event
  const wxContext = cloud.getWXContext()

  try {
    // 验证用户权限
    const userQuery = await db.collection('users').doc(userId).get()
    if (!userQuery.data) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userQuery.data
    
    // 检查openid是否匹配（安全验证）
    if (user.openid !== wxContext.openid) {
      return {
        success: false,
        message: '无权限修改此用户信息'
      }
    }

    // 验证手机号是否已被其他用户使用
    if (updateData.phone && updateData.phone !== user.phone) {
      const phoneQuery = await db.collection('users').where({
        phone: updateData.phone,
        _id: db.command.neq(userId)
      }).get()

      if (phoneQuery.data.length > 0) {
        return {
          success: false,
          message: '该手机号已被其他用户使用'
        }
      }
    }

    // 更新用户信息
    const updateResult = await db.collection('users').doc(userId).update({
      data: {
        ...updateData,
        updateTime: new Date()
      }
    })

    // 记录操作日志
    await db.collection('system_logs').add({
      data: {
        type: 'user_operation',
        userId: userId,
        action: 'update_profile',
        details: {
          updatedFields: Object.keys(updateData),
          updateData: updateData
        },
        timestamp: new Date()
      }
    })

    return {
      success: true,
      data: {
        updated: updateResult.stats.updated
      },
      message: '用户信息更新成功'
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    return {
      success: false,
      message: '更新用户信息失败',
      error: error.message
    }
  }
}
