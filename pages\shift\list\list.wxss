/* pages/shift/list/list.wxss */
.list-container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 搜索区域 */
.search-section {
  background: #ffffff;
  padding: 20rpx;
  border-bottom: 2rpx solid #ebedf0;
}

/* 筛选标签 */
.filter-tags {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
  border-bottom: 2rpx solid #ebedf0;
}

/* 交班列表 */
.shift-list {
  padding: 20rpx;
}

.shift-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 头部信息 */
.shift-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.shift-info {
  flex: 1;
}

.shift-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.shift-creator {
  font-size: 26rpx;
  color: #969799;
}

.shift-status {
  margin-left: 20rpx;
}

/* 详细信息 */
.shift-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-text {
  font-size: 26rpx;
  color: #646566;
}

/* 统计信息 */
.shift-stats {
  display: flex;
  gap: 30rpx;
  padding: 20rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #969799;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.stat-value.error {
  color: #ee0a24;
}

/* 操作按钮 */
.shift-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 20rpx;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  right: 30rpx;
  bottom: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background: #1989fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(25, 137, 250, 0.3);
  z-index: 1000;
}

/* 筛选弹窗 */
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.filter-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 40rpx;
}

.filter-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 2rpx solid #ebedf0;
}

.filter-actions .van-button {
  flex: 1;
}

/* 二维码弹窗 */
.qrcode-popup {
  padding: 30rpx;
  text-align: center;
}

.qrcode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.qrcode-content {
  margin-bottom: 30rpx;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  border: 2rpx solid #ebedf0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.qrcode-tips {
  font-size: 26rpx;
  color: #969799;
}

.qrcode-actions {
  display: flex;
  gap: 20rpx;
}

.qrcode-actions .van-button {
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .shift-stats {
    gap: 20rpx;
  }
  
  .shift-actions {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .filter-options {
    gap: 8rpx;
  }
}

/* 安全区域适配 */
.list-container {
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.fab {
  bottom: calc(30rpx + env(safe-area-inset-bottom));
}
