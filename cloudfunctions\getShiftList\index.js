// 云函数：获取交班列表
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { 
    page = 1, 
    limit = 20, 
    searchKey, 
    status, 
    shiftType, 
    priority, 
    startDate, 
    endDate,
    creator,
    department 
  } = event

  try {
    let query = db.collection('shift_records')

    // 构建查询条件
    const whereConditions = {}

    // 搜索条件
    if (searchKey) {
      const searchRegex = new RegExp(searchKey, 'i')
      whereConditions.$or = [
        { creatorName: searchRegex },
        { shiftType: searchRegex },
        { department: searchRegex },
        { specialNotes: searchRegex }
      ]
    }

    // 状态筛选
    if (status) {
      whereConditions.status = status
    }

    // 班次类型筛选
    if (shiftType) {
      whereConditions.shiftType = shiftType
    }

    // 优先级筛选
    if (priority) {
      whereConditions.priority = priority
    }

    // 创建人筛选（护士只能看自己的）
    if (creator) {
      whereConditions.creator = creator
    }

    // 科室筛选（护士长可以看本科室的）
    if (department) {
      whereConditions.department = department
    }

    // 时间范围筛选
    if (startDate || endDate) {
      whereConditions.createTime = {}
      if (startDate) {
        whereConditions.createTime[_.gte] = new Date(startDate)
      }
      if (endDate) {
        const endDateTime = new Date(endDate)
        endDateTime.setHours(23, 59, 59, 999)
        whereConditions.createTime[_.lte] = endDateTime
      }
    }

    // 应用查询条件
    if (Object.keys(whereConditions).length > 0) {
      query = query.where(whereConditions)
    }

    // 分页查询
    const skip = (page - 1) * limit
    const result = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    // 获取总数
    const countResult = await query.count()

    // 格式化数据
    const shiftList = result.data.map(shift => ({
      _id: shift._id,
      creator: shift.creator,
      creatorName: shift.creatorName,
      department: shift.department,
      shiftType: shift.shiftType,
      shiftDate: shift.shiftDate,
      priority: shift.priority,
      status: shift.status,
      instrumentCheck: shift.instrumentCheck || [],
      equipmentStatus: shift.equipmentStatus || [],
      specialNotes: shift.specialNotes,
      attachments: shift.attachments || [],
      createTime: shift.createTime,
      updateTime: shift.updateTime,
      handoverTime: shift.handoverTime,
      handoverUser: shift.handoverUser,
      qrCode: shift.qrCode
    }))

    return {
      success: true,
      data: {
        list: shiftList,
        pagination: {
          page: page,
          limit: limit,
          total: countResult.total,
          hasMore: skip + result.data.length < countResult.total
        }
      }
    }
  } catch (error) {
    console.error('获取交班列表失败:', error)
    return {
      success: false,
      message: '获取交班列表失败',
      error: error.message
    }
  }
}
