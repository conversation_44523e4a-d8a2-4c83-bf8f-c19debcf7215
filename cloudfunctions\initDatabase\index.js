// 云函数：初始化数据库
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('开始初始化数据库...')
    
    // 1. 创建用户集合
    await createUsersCollection()
    
    // 2. 创建交班记录集合
    await createShiftRecordsCollection()
    
    // 3. 创建接班日志集合
    await createHandoverLogsCollection()
    
    // 4. 创建器械集合
    await createInstrumentsCollection()
    
    // 5. 创建设备集合
    await createEquipmentsCollection()
    
    // 6. 创建科室集合
    await createDepartmentsCollection()
    
    // 7. 创建通知集合
    await createNotificationsCollection()
    
    // 8. 创建系统日志集合
    await createSystemLogsCollection()
    
    console.log('数据库初始化完成')
    
    return {
      success: true,
      message: '数据库初始化成功',
      timestamp: new Date()
    }
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      message: '数据库初始化失败',
      error: error.message
    }
  }
}

// 创建用户集合
async function createUsersCollection() {
  try {
    // 插入示例数据
    await db.collection('users').add({
      data: {
        openid: 'demo_openid_admin',
        name: '系统管理员',
        phone: '13800138000',
        role: 'admin',
        department: 'cssd',
        avatar: '',
        status: 'active',
        createTime: new Date(),
        updateTime: new Date()
      }
    })
    
    console.log('用户集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) { // 集合已存在
      throw error
    }
  }
}

// 创建交班记录集合
async function createShiftRecordsCollection() {
  try {
    await db.collection('shift_records').add({
      data: {
        creator: 'demo_user_id',
        creatorName: '示例用户',
        department: 'cssd',
        shiftType: '早班',
        shiftDate: '2024-01-01',
        priority: 'normal',
        instrumentCheck: [],
        equipmentStatus: [],
        specialNotes: '示例交班记录',
        attachments: [],
        status: 'pending',
        createTime: new Date(),
        updateTime: new Date()
      }
    })
    
    console.log('交班记录集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) {
      throw error
    }
  }
}

// 创建接班日志集合
async function createHandoverLogsCollection() {
  try {
    await db.collection('handover_logs').add({
      data: {
        shiftId: 'demo_shift_id',
        fromUser: 'demo_from_user',
        toUser: 'demo_to_user',
        handoverTime: new Date(),
        signatureImg: '',
        confirmNotes: '接班确认',
        status: 'completed',
        timestamp: new Date()
      }
    })
    
    console.log('接班日志集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) {
      throw error
    }
  }
}

// 创建器械集合
async function createInstrumentsCollection() {
  try {
    const instruments = [
      { name: '腹腔镜', code: 'FQJ001', category: 'surgical', unit: '套', description: '腹腔镜手术器械包' },
      { name: '持针器', code: 'CZQ001', category: 'surgical', unit: '把', description: '外科持针器' },
      { name: '血管钳', code: 'XGQ001', category: 'surgical', unit: '把', description: '血管止血钳' },
      { name: '呼吸机管路', code: 'HXJGL001', category: 'respiratory', unit: '套', description: '呼吸机专用管路' },
      { name: '心电监护仪', code: 'XDJHY001', category: 'monitoring', unit: '台', description: '多参数监护仪' }
    ]
    
    for (const instrument of instruments) {
      await db.collection('instruments').add({
        data: {
          ...instrument,
          status: 'active',
          createTime: new Date()
        }
      })
    }
    
    console.log('器械集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) {
      throw error
    }
  }
}

// 创建设备集合
async function createEquipmentsCollection() {
  try {
    const equipments = [
      {
        name: '高温灭菌器',
        code: 'GWMJQ001',
        model: 'ST-100',
        department: 'cssd',
        status: 'normal',
        location: '灭菌室A',
        specifications: '容量100L，温度134℃'
      },
      {
        name: '超声清洗机',
        code: 'CSQXJ001',
        model: 'UC-50',
        department: 'cssd',
        status: 'normal',
        location: '清洗室B',
        specifications: '频率40KHz，容量50L'
      }
    ]
    
    for (const equipment of equipments) {
      await db.collection('equipments').add({
        data: {
          ...equipment,
          createTime: new Date(),
          updateTime: new Date()
        }
      })
    }
    
    console.log('设备集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) {
      throw error
    }
  }
}

// 创建科室集合
async function createDepartmentsCollection() {
  try {
    const departments = [
      { code: 'cssd', name: '消毒供应中心', description: '中心供应室' },
      { code: 'or', name: '手术室', description: '外科手术室' },
      { code: 'icu', name: 'ICU', description: '重症监护室' },
      { code: 'emergency', name: '急诊科', description: '急诊医学科' }
    ]
    
    for (const dept of departments) {
      await db.collection('departments').add({
        data: {
          ...dept,
          status: 'active',
          createTime: new Date()
        }
      })
    }
    
    console.log('科室集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) {
      throw error
    }
  }
}

// 创建通知集合
async function createNotificationsCollection() {
  try {
    await db.collection('notifications').add({
      data: {
        title: '系统初始化完成',
        content: '数据库初始化成功，系统已准备就绪',
        type: 'system',
        targetUsers: [],
        isRead: false,
        createTime: new Date()
      }
    })
    
    console.log('通知集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) {
      throw error
    }
  }
}

// 创建系统日志集合
async function createSystemLogsCollection() {
  try {
    await db.collection('system_logs').add({
      data: {
        type: 'system_init',
        action: 'database_init',
        userId: 'system',
        details: {
          message: '数据库初始化',
          collections: ['users', 'shift_records', 'handover_logs', 'instruments', 'equipments', 'departments', 'notifications']
        },
        timestamp: new Date()
      }
    })
    
    console.log('系统日志集合创建成功')
  } catch (error) {
    if (error.errCode !== -502002) {
      throw error
    }
  }
}
