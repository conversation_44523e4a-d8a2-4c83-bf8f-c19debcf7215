/**app.wxss**/
@import '@vant/weapp/common/index.wxss';

/* 全局样式 */
page {
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.page-container {
  padding: 0 20rpx 20rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
}

.card-content {
  font-size: 28rpx;
  color: #646566;
  line-height: 1.6;
}

/* 按钮样式 */
.btn-primary {
  background-color: #1989fa !important;
  border-color: #1989fa !important;
  color: #ffffff !important;
}

.btn-success {
  background-color: #07c160 !important;
  border-color: #07c160 !important;
  color: #ffffff !important;
}

.btn-warning {
  background-color: #ff976a !important;
  border-color: #ff976a !important;
  color: #ffffff !important;
}

.btn-danger {
  background-color: #ee0a24 !important;
  border-color: #ee0a24 !important;
  color: #ffffff !important;
}

/* 状态样式 */
.status-pending {
  color: #ff976a;
  background-color: #fff7e6;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.status-confirmed {
  color: #07c160;
  background-color: #f0f9ff;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.status-rejected {
  color: #ee0a24;
  background-color: #fff1f0;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 列表样式 */
.list-item {
  background-color: #ffffff;
  padding: 24rpx;
  margin-bottom: 2rpx;
  border-left: 6rpx solid transparent;
  transition: all 0.3s ease;
}

.list-item:first-child {
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.list-item:last-child {
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  margin-bottom: 20rpx;
}

.list-item.active {
  border-left-color: #1989fa;
  background-color: #f8f9fa;
}

.list-item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: #969799;
  line-height: 1.4;
}

.list-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

/* 表单样式 */
.form-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.form-section-title {
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  background-color: #f8f9fa;
  border-bottom: 2rpx solid #ebedf0;
}

.form-item {
  padding: 24rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #323233;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-required {
  color: #ee0a24;
  margin-right: 4rpx;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.ml-10 {
  margin-left: 10rpx;
}

.mr-10 {
  margin-right: 10rpx;
}

.p-20 {
  padding: 20rpx;
}

.px-20 {
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.py-20 {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #969799;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #969799;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
