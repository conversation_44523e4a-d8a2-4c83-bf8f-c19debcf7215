// 云函数：获取用户统计信息
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { userId } = event

  try {
    // 获取用户基本信息
    const userQuery = await db.collection('users').doc(userId).get()
    if (!userQuery.data) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userQuery.data
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    // 统计交班次数
    const shiftQuery = await db.collection('shift_records').where({
      creator: userId
    }).count()

    // 统计接班次数
    const handoverQuery = await db.collection('handover_logs').where({
      toUser: userId
    }).count()

    // 统计签名次数
    const signatureQuery = await db.collection('handover_logs').where({
      toUser: userId,
      signatureImg: _.exists(true)
    }).count()

    // 统计最近30天的交班次数
    const recentShiftQuery = await db.collection('shift_records').where({
      creator: userId,
      createTime: _.gte(thirtyDaysAgo)
    }).count()

    // 统计最近30天的接班次数
    const recentHandoverQuery = await db.collection('handover_logs').where({
      toUser: userId,
      timestamp: _.gte(thirtyDaysAgo)
    }).count()

    // 获取最近的交班记录
    const recentShiftsQuery = await db.collection('shift_records')
      .where({
        creator: userId
      })
      .orderBy('createTime', 'desc')
      .limit(5)
      .get()

    // 获取最近的接班记录
    const recentHandoversQuery = await db.collection('handover_logs')
      .where({
        toUser: userId
      })
      .orderBy('timestamp', 'desc')
      .limit(5)
      .get()

    // 计算工作质量评分（基于交班完成率、及时性等）
    const qualityScore = await this.calculateQualityScore(userId)

    return {
      success: true,
      data: {
        // 基本统计
        shiftCount: shiftQuery.total,
        handoverCount: handoverQuery.total,
        signatureCount: signatureQuery.total,
        
        // 最近统计
        recentShiftCount: recentShiftQuery.total,
        recentHandoverCount: recentHandoverQuery.total,
        
        // 最近记录
        recentShifts: recentShiftsQuery.data,
        recentHandovers: recentHandoversQuery.data,
        
        // 质量评分
        qualityScore: qualityScore,
        
        // 用户等级（基于经验值）
        userLevel: this.calculateUserLevel(shiftQuery.total + handoverQuery.total),
        
        // 统计时间
        statisticsTime: now
      }
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return {
      success: false,
      message: '获取用户统计失败',
      error: error.message
    }
  }
}

// 计算质量评分
async function calculateQualityScore(userId) {
  try {
    // 获取用户最近30天的交班记录
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    const shiftsQuery = await db.collection('shift_records').where({
      creator: userId,
      createTime: _.gte(thirtyDaysAgo)
    }).get()

    if (shiftsQuery.data.length === 0) {
      return 85 // 默认评分
    }

    let totalScore = 0
    let scoreCount = 0

    for (const shift of shiftsQuery.data) {
      let score = 100

      // 根据交班完成情况扣分
      if (shift.status === 'cancelled') {
        score -= 30
      } else if (shift.status === 'pending') {
        score -= 10
      }

      // 根据器械差异情况扣分
      if (shift.issueCount > 0) {
        score -= Math.min(shift.issueCount * 5, 20)
      }

      // 根据优先级扣分
      if (shift.priority === 'urgent') {
        score += 5 // 紧急交班加分
      }

      totalScore += Math.max(score, 0)
      scoreCount++
    }

    return Math.round(totalScore / scoreCount)
  } catch (error) {
    console.error('计算质量评分失败:', error)
    return 85
  }
}

// 计算用户等级
function calculateUserLevel(totalOperations) {
  if (totalOperations >= 1000) return { level: 5, name: '专家级' }
  if (totalOperations >= 500) return { level: 4, name: '高级' }
  if (totalOperations >= 200) return { level: 3, name: '中级' }
  if (totalOperations >= 50) return { level: 2, name: '初级' }
  return { level: 1, name: '新手' }
}
