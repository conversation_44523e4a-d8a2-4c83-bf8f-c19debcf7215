// pages/shift/create/create.js
const app = getApp()
const util = require('../../../utils/util.js')
const api = require('../../../utils/api.js')
const config = require('../../../utils/config.js')

Page({
  data: {
    // 表单数据
    formData: {
      shiftType: '',
      shiftDate: '',
      priority: 'normal',
      instrumentCheck: [],
      equipmentStatus: [],
      specialNotes: '',
      attachments: []
    },
    
    // 显示数据
    priorityText: '普通',
    currentDate: new Date().getTime(),
    
    // 弹窗状态
    showShiftTypePopup: false,
    showDatePopup: false,
    showPriorityPopup: false,
    showInstrumentPopup: false,
    showEquipmentPopup: false,
    
    // 选择器数据
    shiftTypeColumns: [],
    priorityColumns: [
      { text: '普通', value: 'normal' },
      { text: '紧急', value: 'urgent' },
      { text: '重要', value: 'important' }
    ],
    
    // 器械相关
    instrumentList: [],
    filteredInstruments: [],
    instrumentSearchKey: '',
    
    // 设备相关
    equipmentList: [],
    equipmentStatusOptions: [
      { text: '正常', value: 'normal' },
      { text: '故障', value: 'fault' },
      { text: '维修中', value: 'repairing' },
      { text: '停用', value: 'disabled' }
    ],
    
    // 状态
    submitLoading: false,
    canSubmit: false
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.checkFormValidity()
  },

  // 初始化页面
  async initPage() {
    try {
      // 设置默认交班日期为今天
      const today = util.formatDate(new Date())
      this.setData({
        'formData.shiftDate': today,
        currentDate: new Date().getTime()
      })

      // 加载基础数据
      await Promise.all([
        this.loadShiftTypes(),
        this.loadInstruments(),
        this.loadEquipments()
      ])
    } catch (error) {
      console.error('初始化页面失败:', error)
      util.showToast('初始化失败')
    }
  },

  // 加载班次类型
  async loadShiftTypes() {
    try {
      const shiftTypes = config.shiftTypes || [
        { text: '早班', value: 'morning' },
        { text: '中班', value: 'afternoon' },
        { text: '晚班', value: 'evening' },
        { text: '夜班', value: 'night' }
      ]
      this.setData({ shiftTypeColumns: shiftTypes })
    } catch (error) {
      console.error('加载班次类型失败:', error)
    }
  },

  // 加载器械列表
  async loadInstruments() {
    try {
      const result = await api.callFunction('getInstruments')
      if (result.success) {
        this.setData({
          instrumentList: result.data,
          filteredInstruments: result.data
        })
      }
    } catch (error) {
      console.error('加载器械列表失败:', error)
    }
  },

  // 加载设备列表
  async loadEquipments() {
    try {
      const result = await api.callFunction('getEquipments')
      if (result.success) {
        this.setData({ equipmentList: result.data })
      }
    } catch (error) {
      console.error('加载设备列表失败:', error)
    }
  },

  // 选择班次类型
  selectShiftType() {
    this.setData({ showShiftTypePopup: true })
  },

  closeShiftTypePopup() {
    this.setData({ showShiftTypePopup: false })
  },

  onShiftTypeConfirm(e) {
    const { value } = e.detail
    const shiftType = this.data.shiftTypeColumns.find(item => item.value === value[0])
    this.setData({
      'formData.shiftType': shiftType ? shiftType.text : '',
      showShiftTypePopup: false
    })
    this.checkFormValidity()
  },

  // 选择交班日期
  selectShiftDate() {
    this.setData({ showDatePopup: true })
  },

  closeDatePopup() {
    this.setData({ showDatePopup: false })
  },

  onDateConfirm(e) {
    const date = new Date(e.detail)
    this.setData({
      'formData.shiftDate': util.formatDate(date),
      currentDate: e.detail,
      showDatePopup: false
    })
    this.checkFormValidity()
  },

  // 选择优先级
  selectPriority() {
    this.setData({ showPriorityPopup: true })
  },

  closePriorityPopup() {
    this.setData({ showPriorityPopup: false })
  },

  onPriorityConfirm(e) {
    const { value } = e.detail
    const priority = this.data.priorityColumns.find(item => item.value === value[0])
    this.setData({
      'formData.priority': priority ? priority.value : 'normal',
      priorityText: priority ? priority.text : '普通',
      showPriorityPopup: false
    })
  },

  // 添加器械
  addInstrument() {
    this.setData({ showInstrumentPopup: true })
  },

  closeInstrumentPopup() {
    this.setData({ showInstrumentPopup: false })
  },

  // 器械搜索
  onInstrumentSearch(e) {
    const searchKey = e.detail.value.toLowerCase()
    const filtered = this.data.instrumentList.filter(item => 
      item.name.toLowerCase().includes(searchKey) || 
      item.code.toLowerCase().includes(searchKey)
    )
    this.setData({
      instrumentSearchKey: searchKey,
      filteredInstruments: filtered
    })
  },

  // 选择器械
  selectInstrument(e) {
    const instrument = e.currentTarget.dataset.instrument
    
    // 检查是否已添加
    const exists = this.data.formData.instrumentCheck.some(item => item.code === instrument.code)
    if (exists) {
      util.showToast('该器械已添加')
      return
    }

    const newInstrument = {
      name: instrument.name,
      code: instrument.code,
      unit: instrument.unit,
      plan: 0,
      actual: 0,
      reason: ''
    }

    this.setData({
      'formData.instrumentCheck': [...this.data.formData.instrumentCheck, newInstrument],
      showInstrumentPopup: false
    })
    this.checkFormValidity()
  },

  // 移除器械
  removeInstrument(e) {
    const index = e.currentTarget.dataset.index
    const instrumentCheck = this.data.formData.instrumentCheck.filter((_, i) => i !== index)
    this.setData({ 'formData.instrumentCheck': instrumentCheck })
    this.checkFormValidity()
  },

  // 器械计划数量变化
  onInstrumentPlanChange(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail
    this.setData({
      [`formData.instrumentCheck[${index}].plan`]: value
    })
    this.checkFormValidity()
  },

  // 器械实际数量变化
  onInstrumentActualChange(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail
    this.setData({
      [`formData.instrumentCheck[${index}].actual`]: value
    })
    this.checkFormValidity()
  },

  // 器械差异原因变化
  onInstrumentReasonChange(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    this.setData({
      [`formData.instrumentCheck[${index}].reason`]: value
    })
  },

  // 检查表单有效性
  checkFormValidity() {
    const { formData } = this.data
    const canSubmit = formData.shiftType && formData.shiftDate
    this.setData({ canSubmit })
  },

  // 添加设备
  addEquipment() {
    // 简化版：直接添加一个空设备项
    const newEquipment = {
      name: '',
      code: '',
      status: 'normal',
      remark: ''
    }
    this.setData({
      'formData.equipmentStatus': [...this.data.formData.equipmentStatus, newEquipment]
    })
  },

  // 移除设备
  removeEquipment(e) {
    const index = e.currentTarget.dataset.index
    const equipmentStatus = this.data.formData.equipmentStatus.filter((_, i) => i !== index)
    this.setData({ 'formData.equipmentStatus': equipmentStatus })
  },

  // 设备编号变化
  onEquipmentCodeChange(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    this.setData({
      [`formData.equipmentStatus[${index}].code`]: value
    })
  },

  // 设备状态选择
  selectEquipmentStatus(e) {
    const index = e.currentTarget.dataset.index
    const options = this.data.equipmentStatusOptions.map(item => item.text)

    wx.showActionSheet({
      itemList: options,
      success: (res) => {
        const selectedOption = this.data.equipmentStatusOptions[res.tapIndex]
        this.setData({
          [`formData.equipmentStatus[${index}].status`]: selectedOption.text
        })
      }
    })
  },

  // 设备备注变化
  onEquipmentRemarkChange(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    this.setData({
      [`formData.equipmentStatus[${index}].remark`]: value
    })
  },

  // 特殊事项变化
  onSpecialNotesChange(e) {
    this.setData({
      'formData.specialNotes': e.detail.value
    })
  },

  // 上传附件
  uploadAttachment() {
    wx.chooseMedia({
      count: 5,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        try {
          util.showLoading('上传中...')

          const uploadPromises = res.tempFiles.map(async (file) => {
            const uploadResult = await api.uploadFile(file.tempFilePath, 'attachments/')
            return {
              name: `附件_${Date.now()}`,
              url: uploadResult.fileID,
              size: util.formatFileSize(file.size)
            }
          })

          const attachments = await Promise.all(uploadPromises)
          this.setData({
            'formData.attachments': [...this.data.formData.attachments, ...attachments]
          })

          util.showToast('上传成功', 'success')
        } catch (error) {
          console.error('上传附件失败:', error)
          util.showToast('上传失败')
        } finally {
          util.hideLoading()
        }
      }
    })
  },

  // 预览附件
  previewAttachment(e) {
    const index = e.currentTarget.dataset.index
    const attachment = this.data.formData.attachments[index]

    wx.previewImage({
      current: attachment.url,
      urls: this.data.formData.attachments.map(item => item.url)
    })
  },

  // 移除附件
  removeAttachment(e) {
    const index = e.currentTarget.dataset.index
    const attachments = this.data.formData.attachments.filter((_, i) => i !== index)
    this.setData({ 'formData.attachments': attachments })
  },

  // 提交交班
  async submitShift() {
    try {
      if (!this.data.canSubmit) {
        util.showToast('请完善必填信息')
        return
      }

      this.setData({ submitLoading: true })

      const userInfo = app.globalData.userInfo
      const shiftData = {
        ...this.data.formData,
        creator: userInfo.userId,
        creatorName: userInfo.name,
        department: userInfo.department,
        createTime: new Date(),
        status: 'pending'
      }

      const result = await api.callFunction('createShift', shiftData)

      if (result.success) {
        util.showToast('交班创建成功', 'success')

        // 返回上一页并刷新
        setTimeout(() => {
          wx.navigateBack({
            delta: 1
          })
        }, 1500)
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('提交交班失败:', error)
      util.showToast('提交失败，请重试')
    } finally {
      this.setData({ submitLoading: false })
    }
  }
})
