/* pages/profile/profile.wxss */
.profile-container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.user-role {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.department {
  margin-left: 12rpx;
  font-size: 26rpx;
  opacity: 0.8;
}

.user-phone {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 统计信息 */
.stats-row {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 功能菜单 */
.menu-section,
.work-section,
.system-section {
  margin: 20rpx 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 30rpx;
}

/* 编辑弹窗 */
.edit-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.edit-form {
  flex: 1;
  padding: 30rpx;
}

.popup-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 2rpx solid #ebedf0;
}

.popup-footer .van-button {
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-row {
    padding: 20rpx;
  }
  
  .stat-number {
    font-size: 36rpx;
  }
  
  .stat-label {
    font-size: 22rpx;
  }
}

/* 安全区域适配 */
.profile-container {
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}
