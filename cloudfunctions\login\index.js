// 云函数：用户登录
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { code, userInfo } = event
  const wxContext = cloud.getWXContext()

  try {
    // 获取用户openid
    const { openid, unionid } = wxContext

    // 查询用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()

    let userData
    
    if (userQuery.data.length > 0) {
      // 用户已存在，更新信息
      userData = userQuery.data[0]
      
      await db.collection('users').doc(userData._id).update({
        data: {
          avatarUrl: userInfo.avatarUrl,
          nickName: userInfo.nickName,
          lastLoginTime: new Date(),
          updateTime: new Date()
        }
      })
      
      userData.avatarUrl = userInfo.avatarUrl
      userData.nickName = userInfo.nickName
    } else {
      // 新用户，创建记录
      const createResult = await db.collection('users').add({
        data: {
          openid: openid,
          unionid: unionid,
          avatarUrl: userInfo.avatarUrl,
          nickName: userInfo.nickName,
          name: '', // 真实姓名，需要后续完善
          phone: '', // 手机号，需要后续绑定
          role: 'nurse', // 默认角色为护士
          department: '', // 部门，需要后续设置
          status: 'active', // 用户状态
          createTime: new Date(),
          updateTime: new Date(),
          lastLoginTime: new Date()
        }
      })
      
      // 获取新创建的用户信息
      const newUserQuery = await db.collection('users').doc(createResult._id).get()
      userData = newUserQuery.data
      userData._id = createResult._id
    }

    // 记录登录日志
    await db.collection('system_logs').add({
      data: {
        type: 'login',
        userId: userData._id,
        openid: openid,
        action: 'wx_login',
        ip: event.userIP || '',
        userAgent: event.userAgent || '',
        timestamp: new Date()
      }
    })

    return {
      success: true,
      data: {
        userInfo: {
          userId: userData._id,
          openid: openid,
          name: userData.name || userData.nickName,
          avatarUrl: userData.avatarUrl,
          role: userData.role,
          department: userData.department,
          phone: userData.phone,
          status: userData.status
        },
        openid: openid,
        isNewUser: userQuery.data.length === 0
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败，请重试',
      error: error.message
    }
  }
}
