{"name": "cssd-shift-system", "version": "1.0.0", "description": "供应室护士交班小程序", "main": "app.js", "scripts": {"dev": "npm run build:npm", "build:npm": "npm run build && npm run build:miniprogram", "build": "npm install", "build:miniprogram": "echo 'Building miniprogram...'", "lint": "eslint --ext .js .", "test": "echo 'No tests specified'"}, "dependencies": {"@vant/weapp": "^1.11.6"}, "devDependencies": {"eslint": "^8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/cssd-shift-system.git"}, "keywords": ["miniprogram", "wechat", "healthcare", "shift-management", "cssd"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/your-username/cssd-shift-system/issues"}, "homepage": "https://github.com/your-username/cssd-shift-system#readme"}