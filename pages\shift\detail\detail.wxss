/* pages/shift/detail/detail.wxss */
.detail-container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding: 20rpx;
}

.detail-content {
  padding-bottom: 40rpx;
}

.notes-content {
  padding: 20rpx 0;
  line-height: 1.6;
  color: #323233;
}

.attachments {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx;
}

.attachment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.file-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  border: 2rpx solid #ebedf0;
}

.file-name {
  font-size: 24rpx;
  color: #646566;
  max-width: 120rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 40rpx 20rpx;
}

.status-normal {
  color: #07c160 !important;
}

.status-error {
  color: #ee0a24 !important;
}
