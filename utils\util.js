// 工具函数库
const config = require('./config.js')

/**
 * 格式化时间
 * @param {Date|string|number} date 时间
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 获取相对时间
 * @param {Date|string|number} date 时间
 * @returns {string} 相对时间字符串
 */
function getRelativeTime(date) {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else {
    return formatTime(date, 'YYYY-MM-DD')
  }
}

/**
 * 生成唯一ID
 * @param {string} prefix 前缀
 * @returns {string} 唯一ID
 */
function generateId(prefix = '') {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}${timestamp}${random}`
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
function debounce(func, delay = 300) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
function throttle(func, delay = 300) {
  let timer = null
  return function(...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}

/**
 * 存储数据到本地
 * @param {string} key 键名
 * @param {any} data 数据
 * @param {number} expire 过期时间（毫秒）
 */
function setStorage(key, data, expire = 0) {
  try {
    const item = {
      data: data,
      timestamp: Date.now(),
      expire: expire
    }
    wx.setStorageSync(key, JSON.stringify(item))
  } catch (error) {
    console.error('存储数据失败:', error)
  }
}

/**
 * 从本地获取数据
 * @param {string} key 键名
 * @returns {any} 数据
 */
function getStorage(key) {
  try {
    const item = wx.getStorageSync(key)
    if (!item) return null
    
    const parsed = JSON.parse(item)
    const now = Date.now()
    
    // 检查是否过期
    if (parsed.expire > 0 && now - parsed.timestamp > parsed.expire) {
      wx.removeStorageSync(key)
      return null
    }
    
    return parsed.data
  } catch (error) {
    console.error('获取数据失败:', error)
    return null
  }
}

/**
 * 清除本地存储
 * @param {string} key 键名，不传则清除所有
 */
function clearStorage(key) {
  try {
    if (key) {
      wx.removeStorageSync(key)
    } else {
      wx.clearStorageSync()
    }
  } catch (error) {
    console.error('清除存储失败:', error)
  }
}

/**
 * 显示Toast提示
 * @param {string} title 提示内容
 * @param {string} icon 图标类型
 * @param {number} duration 显示时长
 */
function showToast(title, icon = 'none', duration = 2000) {
  wx.showToast({
    title: title,
    icon: icon,
    duration: duration,
    mask: true
  })
}

/**
 * 显示加载提示
 * @param {string} title 提示内容
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title: title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading()
}

/**
 * 显示确认对话框
 * @param {string} content 内容
 * @param {string} title 标题
 * @returns {Promise<boolean>} 用户选择结果
 */
function showConfirm(content, title = '提示') {
  return new Promise((resolve) => {
    wx.showModal({
      title: title,
      content: content,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 页面跳转
 * @param {string} url 页面路径
 * @param {object} params 参数
 * @param {string} type 跳转类型
 */
function navigateTo(url, params = {}, type = 'navigateTo') {
  let fullUrl = url
  
  // 添加参数
  if (Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')
    fullUrl += `?${queryString}`
  }
  
  const navigateMap = {
    navigateTo: wx.navigateTo,
    redirectTo: wx.redirectTo,
    reLaunch: wx.reLaunch,
    switchTab: wx.switchTab,
    navigateBack: wx.navigateBack
  }
  
  const navigateFunc = navigateMap[type] || wx.navigateTo
  
  navigateFunc({
    url: fullUrl,
    fail: (error) => {
      console.error('页面跳转失败:', error)
      showToast('页面跳转失败')
    }
  })
}

/**
 * 获取页面参数
 * @returns {object} 页面参数对象
 */
function getPageParams() {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage.options || {}
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 验证结果
 */
function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 验证结果
 */
function validateIdCard(idCard) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 脱敏处理
 * @param {string} str 原始字符串
 * @param {string} type 脱敏类型
 * @returns {string} 脱敏后的字符串
 */
function maskSensitiveInfo(str, type = 'phone') {
  if (!str) return ''
  
  switch (type) {
    case 'phone':
      return str.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    case 'idCard':
      return str.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    case 'name':
      if (str.length <= 2) {
        return str.charAt(0) + '*'
      } else {
        return str.charAt(0) + '*'.repeat(str.length - 2) + str.charAt(str.length - 1)
      }
    default:
      return str
  }
}

module.exports = {
  formatTime,
  getRelativeTime,
  generateId,
  deepClone,
  debounce,
  throttle,
  setStorage,
  getStorage,
  clearStorage,
  showToast,
  showLoading,
  hideLoading,
  showConfirm,
  navigateTo,
  getPageParams,
  formatFileSize,
  validatePhone,
  validateIdCard,
  maskSensitiveInfo
}
