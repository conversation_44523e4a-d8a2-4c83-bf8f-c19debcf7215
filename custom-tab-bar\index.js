// custom-tab-bar/index.js
Component({
  data: {
    selected: 0,
    color: "#7A7E83",
    selectedColor: "#1989fa",
    list: [
      {
        pagePath: "/pages/index/index",
        iconPath: "wap-home-o",
        selectedIconPath: "wap-home",
        text: "首页"
      },
      {
        pagePath: "/pages/shift/list/list",
        iconPath: "exchange",
        selectedIconPath: "exchange",
        text: "交班"
      },
      {
        pagePath: "/pages/message/message",
        iconPath: "chat-o",
        selectedIconPath: "chat",
        text: "消息"
      },
      {
        pagePath: "/pages/dashboard/dashboard",
        iconPath: "bar-chart-o",
        selectedIconPath: "bar-chart-o",
        text: "看板"
      },
      {
        pagePath: "/pages/profile/profile",
        iconPath: "user-o",
        selectedIconPath: "user",
        text: "我的"
      }
    ]
  },
  
  attached() {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = '/' + currentPage.route
    
    // 设置当前选中的tab
    const selected = this.data.list.findIndex(item => item.pagePath === currentRoute)
    if (selected !== -1) {
      this.setData({ selected })
    }
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      const index = data.index
      
      if (this.data.selected === index) {
        return
      }
      
      wx.switchTab({
        url,
        success: () => {
          this.setData({ selected: index })
        }
      })
    }
  }
})
