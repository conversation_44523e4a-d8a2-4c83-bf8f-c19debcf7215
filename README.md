# 供应室护士交班小程序

一个专为医疗机构供应室设计的护士交班管理系统，基于微信小程序开发，提供完整的交班流程管理、器械清点、电子签名等功能。

## 🚀 功能特性

### 核心功能
- **用户系统**：微信一键登录、角色权限管理、电子签名
- **交班管理**：新建交班、班次选择、器械清点、特殊事项记录
- **接班流程**：扫码交接、差异确认、电子双签、历史追溯
- **消息中心**：微信订阅消息、紧急通知、未读标识
- **数据看板**：实时统计、质量分析、预警大屏

### 技术特性
- **智能识别**：OCR识别器械编号、语音转文字
- **离线能力**：本地缓存、增量同步
- **安全设计**：数据加密、操作审计、双人校验
- **工作流引擎**：状态机管理、自动化流程

## 🏗️ 技术架构

### 前端架构
- **框架**：微信小程序原生框架（WXML+WXSS+JavaScript）
- **UI组件**：Vant Weapp UI组件库
- **状态管理**：小程序自带的globalData和Storage

### 后端架构
- **云服务**：腾讯云开发（TCB）
- **数据库**：JSON文档型数据库
- **云函数**：Node.js 14
- **云存储**：文件/图片存储

### 安全架构
- **身份认证**：微信登录鉴权（openid验证）
- **数据加密**：敏感字段AES加密
- **权限控制**：RBAC角色模型（护士/护士长/管理员）

## 📁 项目结构

```
cssd-shift-system/
├── pages/                  # 页面文件
│   ├── index/              # 首页
│   ├── login/              # 登录页
│   ├── shift/              # 交班相关页面
│   │   ├── create/         # 新建交班
│   │   ├── list/           # 交班列表
│   │   └── detail/         # 交班详情
│   ├── handover/           # 交接相关页面
│   │   ├── scan/           # 扫码交接
│   │   └── confirm/        # 确认交接
│   ├── message/            # 消息中心
│   ├── dashboard/          # 数据看板
│   ├── profile/            # 个人中心
│   └── signature/          # 电子签名
├── utils/                  # 工具类
│   ├── config.js           # 配置文件
│   ├── util.js             # 通用工具函数
│   └── api.js              # API接口封装
├── cloudfunctions/         # 云函数
│   ├── login/              # 登录函数
│   ├── createShift/        # 创建交班
│   └── ...                 # 其他云函数
├── database/               # 数据库脚本
│   └── init.js             # 初始化脚本
├── images/                 # 图片资源
├── app.js                  # 应用入口
├── app.json                # 应用配置
├── app.wxss                # 全局样式
└── project.config.json     # 项目配置
```

## 🗄️ 数据库设计

### 核心表结构

#### 用户表 (users)
```json
{
  "_id": "U2024001",
  "openid": "wx_openid",
  "name": "张护士",
  "role": "nurse",
  "phone": "138****1234",
  "department": "手术器械组",
  "status": "active",
  "createTime": "2024-06-26T08:00:00Z"
}
```

#### 交班记录表 (shift_records)
```json
{
  "shiftId": "S2024062612001",
  "type": "早班",
  "creator": "U2024001",
  "instrumentCheck": [
    { "name": "腹腔镜", "plan": 5, "actual": 5 },
    { "name": "持针器", "plan": 8, "actual": 7 }
  ],
  "status": "pending",
  "createTime": "2024-06-26T08:00:00Z"
}
```

#### 交接日志表 (handover_logs)
```json
{
  "recordId": "S2024062612001",
  "fromUser": "U2024001",
  "toUser": "U2024002",
  "signatureImg": "cloud://sign1.png",
  "timestamp": "2024-06-26T08:30:00Z"
}
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具 1.06.0+
- Node.js 14+
- 微信小程序账号
- 腾讯云开发环境

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/cssd-shift-system.git
cd cssd-shift-system
```

2. **安装依赖**
```bash
npm install
```

3. **配置小程序**
- 在微信开发者工具中导入项目
- 修改 `project.config.json` 中的 `appid`
- 配置云开发环境ID

4. **初始化数据库**
- 在云开发控制台运行 `database/init.js` 脚本
- 创建必要的数据库集合和索引

5. **部署云函数**
```bash
# 在微信开发者工具中右键云函数目录
# 选择"上传并部署：云端安装依赖"
```

6. **配置权限**
- 设置云数据库权限
- 配置云存储权限
- 设置订阅消息模板

### 开发调试

1. **启动开发服务器**
```bash
npm run dev
```

2. **在微信开发者工具中预览**
- 点击"编译"按钮
- 使用真机调试测试功能

## 📱 功能演示

### 主要页面
- **首页**：快捷操作、待办事项、最近交班、统计概览
- **交班管理**：新建交班、器械清点、特殊事项记录
- **接班流程**：扫码交接、差异确认、电子签名
- **消息中心**：通知消息、系统公告
- **数据看板**：统计图表、质量分析

### 核心流程
1. **交班流程**：选择班次 → 器械清点 → 特殊事项 → 提交审核
2. **接班流程**：扫码识别 → 核对信息 → 确认差异 → 电子签名
3. **消息通知**：自动推送 → 及时提醒 → 状态更新

## 🔧 配置说明

### 云开发配置
```javascript
// utils/config.js
const config = {
  cloud: {
    env: 'your-cloud-env-id', // 替换为你的云开发环境ID
    timeout: 10000,
    retryTimes: 3
  }
}
```

### 权限配置
- **护士**：创建交班、接班确认
- **护士长**：审核交班、查看统计
- **管理员**：系统管理、用户管理

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址：[GitHub](https://github.com/your-username/cssd-shift-system)
- 问题反馈：[Issues](https://github.com/your-username/cssd-shift-system/issues)
- 技术支持：<EMAIL>

## 🙏 致谢

感谢以下开源项目：
- [Vant Weapp](https://vant-contrib.gitee.io/vant-weapp/) - UI组件库
- [微信小程序](https://developers.weixin.qq.com/miniprogram/dev/framework/) - 开发框架
- [腾讯云开发](https://cloud.tencent.com/product/tcb) - 后端服务
