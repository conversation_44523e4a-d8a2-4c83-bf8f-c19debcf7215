// pages/profile/profile.js
const app = getApp()
const util = require('../../utils/util.js')
const api = require('../../utils/api.js')
const config = require('../../utils/config.js')

Page({
  data: {
    // 用户信息
    userInfo: {},
    userStats: {},
    hasSignature: false,
    
    // 显示数据
    roleText: '',
    maskedPhone: '',
    recentShiftCount: 0,
    recentHandoverCount: 0,
    version: '1.0.0',
    
    // 弹窗状态
    showEditPopup: false,
    showDepartmentPopup: false,
    
    // 编辑表单
    editForm: {
      name: '',
      phone: '',
      department: '',
      jobNumber: ''
    },
    
    // 部门选择
    departmentColumns: [],
    
    // 加载状态
    saveLoading: false,
    logoutLoading: false
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadUserData()
  },

  // 初始化页面
  initPage() {
    this.setData({
      version: app.globalData.systemConfig.version
    })
    this.loadDepartments()
  },

  // 加载用户数据
  async loadUserData() {
    try {
      const userInfo = app.globalData.userInfo || util.getStorage('userInfo')
      if (!userInfo) {
        wx.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      // 获取用户统计信息
      const stats = await this.getUserStats(userInfo.userId)
      
      // 检查是否有电子签名
      const hasSignature = await this.checkUserSignature(userInfo.userId)

      this.setData({
        userInfo: userInfo,
        userStats: stats,
        hasSignature: hasSignature,
        roleText: this.getRoleText(userInfo.role),
        maskedPhone: this.maskPhone(userInfo.phone),
        recentShiftCount: stats.recentShiftCount || 0,
        recentHandoverCount: stats.recentHandoverCount || 0
      })
    } catch (error) {
      console.error('加载用户数据失败:', error)
      util.showToast('加载用户数据失败')
    }
  },

  // 获取用户统计信息
  async getUserStats(userId) {
    try {
      const result = await api.callFunction('getUserStats', { userId })
      return result.success ? result.data : {}
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return {}
    }
  },

  // 检查用户签名
  async checkUserSignature(userId) {
    try {
      const result = await api.callFunction('checkUserSignature', { userId })
      return result.success && result.data.hasSignature
    } catch (error) {
      console.error('检查用户签名失败:', error)
      return false
    }
  },

  // 获取角色文本
  getRoleText(role) {
    const roleMap = {
      'nurse': '护士',
      'head_nurse': '护士长',
      'admin': '管理员'
    }
    return roleMap[role] || '未知角色'
  },

  // 手机号掩码
  maskPhone(phone) {
    if (!phone) return '未绑定手机'
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  },

  // 加载部门列表
  async loadDepartments() {
    try {
      const result = await api.callFunction('getDepartments')
      if (result.success) {
        const columns = result.data.map(dept => ({
          text: dept.name,
          value: dept.code
        }))
        this.setData({ departmentColumns: columns })
      }
    } catch (error) {
      console.error('加载部门列表失败:', error)
    }
  },

  // 更换头像
  changeAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadAvatar(res.tempFiles[0].tempFilePath)
      }
    })
  },

  // 上传头像
  async uploadAvatar(filePath) {
    try {
      util.showLoading('上传中...')
      
      const result = await api.uploadFile(filePath, 'avatars/')
      if (result.success) {
        // 更新用户头像
        await this.updateUserInfo({ avatarUrl: result.fileID })
        util.showToast('头像更新成功', 'success')
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('上传头像失败:', error)
      util.showToast('上传头像失败')
    } finally {
      util.hideLoading()
    }
  },

  // 编辑个人信息
  editProfile() {
    const { userInfo } = this.data
    this.setData({
      editForm: {
        name: userInfo.name || '',
        phone: userInfo.phone || '',
        department: userInfo.department || '',
        jobNumber: userInfo.jobNumber || ''
      },
      showEditPopup: true
    })
  },

  // 关闭编辑弹窗
  closeEditPopup() {
    this.setData({ showEditPopup: false })
  },

  // 编辑表单变化
  onEditFormChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`editForm.${field}`]: value
    })
  },

  // 选择部门
  selectDepartment() {
    this.setData({ showDepartmentPopup: true })
  },

  // 关闭部门选择
  closeDepartmentPopup() {
    this.setData({ showDepartmentPopup: false })
  },

  // 确认部门选择
  onDepartmentConfirm(e) {
    const { value } = e.detail
    const department = this.data.departmentColumns.find(item => item.value === value[0])
    this.setData({
      'editForm.department': department ? department.text : '',
      showDepartmentPopup: false
    })
  },

  // 保存个人信息
  async saveProfile() {
    try {
      const { editForm } = this.data
      
      // 验证必填字段
      if (!editForm.name.trim()) {
        util.showToast('请输入真实姓名')
        return
      }
      
      if (!editForm.phone.trim()) {
        util.showToast('请输入手机号码')
        return
      }
      
      if (!util.validatePhone(editForm.phone)) {
        util.showToast('请输入正确的手机号码')
        return
      }

      this.setData({ saveLoading: true })

      // 更新用户信息
      await this.updateUserInfo({
        name: editForm.name.trim(),
        phone: editForm.phone.trim(),
        department: editForm.department,
        jobNumber: editForm.jobNumber.trim()
      })

      util.showToast('保存成功', 'success')
      this.setData({ showEditPopup: false })
      this.loadUserData()
    } catch (error) {
      console.error('保存个人信息失败:', error)
      util.showToast('保存失败，请重试')
    } finally {
      this.setData({ saveLoading: false })
    }
  },

  // 更新用户信息
  async updateUserInfo(updateData) {
    const result = await api.callFunction('updateUserInfo', {
      userId: this.data.userInfo.userId,
      updateData: updateData
    })
    
    if (result.success) {
      // 更新本地用户信息
      const newUserInfo = { ...this.data.userInfo, ...updateData }
      app.globalData.userInfo = newUserInfo
      util.setStorage('userInfo', newUserInfo)
      return result
    } else {
      throw new Error(result.message || '更新失败')
    }
  },

  // 管理电子签名
  manageSignature() {
    wx.navigateTo({
      url: '/pages/signature/signature'
    })
  },

  // 安全设置
  securitySettings() {
    util.showToast('功能开发中')
  },

  // 查看我的交班
  viewMyShifts() {
    wx.navigateTo({
      url: '/pages/shift/list/list?type=my'
    })
  },

  // 查看我的接班
  viewMyHandovers() {
    wx.navigateTo({
      url: '/pages/handover/list/list?type=my'
    })
  },

  // 查看操作日志
  viewOperationLogs() {
    util.showToast('功能开发中')
  },

  // 消息通知设置
  notificationSettings() {
    util.showToast('功能开发中')
  },

  // 帮助中心
  helpCenter() {
    util.showToast('功能开发中')
  },

  // 关于我们
  aboutUs() {
    util.showToast('功能开发中')
  },

  // 退出登录
  async logout() {
    try {
      const result = await util.showModal('确认退出', '确定要退出登录吗？')
      if (!result.confirm) return

      this.setData({ logoutLoading: true })

      // 清除本地数据
      app.globalData.userInfo = null
      app.globalData.openid = null
      app.globalData.userRole = null
      
      util.removeStorage('userInfo')
      util.removeStorage('openid')

      util.showToast('已退出登录', 'success')
      
      // 跳转到登录页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
    } catch (error) {
      console.error('退出登录失败:', error)
      util.showToast('退出失败，请重试')
    } finally {
      this.setData({ logoutLoading: false })
    }
  }
})
