/* pages/signature/signature.wxss */
.signature-container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding: 20rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #969799;
}

/* 当前签名 */
.current-signature {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20rpx;
}

.signature-preview {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.signature-image {
  width: 200rpx;
  height: 100rpx;
  border: 2rpx solid #ebedf0;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.signature-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.create-time,
.usage-count {
  font-size: 26rpx;
  color: #646566;
}

.signature-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

/* 无签名状态 */
.no-signature {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

/* 签名画板 */
.signature-board {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.board-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.board-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12rpx;
}

.board-tips {
  font-size: 26rpx;
  color: #969799;
}

/* 画布容器 */
.canvas-container {
  border: 2rpx solid #ebedf0;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.signature-canvas {
  width: 100%;
  height: 400rpx;
  background: #ffffff;
  display: block;
}

/* 画板工具栏 */
.canvas-toolbar {
  background: #f7f8fa;
  padding: 20rpx;
  border-top: 2rpx solid #ebedf0;
}

.tool-group {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tool-group:last-child {
  margin-bottom: 0;
}

.tool-label {
  font-size: 26rpx;
  color: #646566;
  margin-right: 16rpx;
  min-width: 120rpx;
}

.tool-value {
  font-size: 26rpx;
  color: #323233;
  margin-left: 16rpx;
  min-width: 60rpx;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  gap: 12rpx;
}

.color-item {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 4rpx solid transparent;
  cursor: pointer;
}

.color-item.active {
  border-color: #1989fa;
}

/* 操作按钮 */
.board-actions {
  display: flex;
  gap: 16rpx;
  justify-content: space-between;
}

.board-actions .van-button {
  flex: 1;
}

/* 使用说明 */
.signature-guide {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.guide-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  font-size: 26rpx;
  color: #646566;
  line-height: 1.6;
}

/* 签名历史 */
.signature-history {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
}

.history-info {
  flex: 1;
}

.history-title {
  font-size: 28rpx;
  color: #323233;
  margin-bottom: 8rpx;
}

.history-time {
  font-size: 24rpx;
  color: #969799;
}

.load-more {
  text-align: center;
  margin-top: 20rpx;
}

/* 预览弹窗 */
.preview-popup {
  padding: 30rpx;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.preview-content {
  text-align: center;
}

.preview-image {
  width: 100%;
  max-height: 300rpx;
  border: 2rpx solid #ebedf0;
  border-radius: 8rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .signature-canvas {
    height: 350rpx;
  }
  
  .tool-group {
    flex-wrap: wrap;
    gap: 8rpx;
  }
  
  .tool-label {
    min-width: 100rpx;
  }
}

/* 安全区域适配 */
.signature-container {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
