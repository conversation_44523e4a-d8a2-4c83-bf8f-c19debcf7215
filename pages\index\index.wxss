/* pages/index/index.wxss */
.container {
  padding: 0;
  background: linear-gradient(180deg, #1989fa 0%, #f7f8fa 300rpx);
  min-height: 100vh;
}

/* 用户头部 */
.user-header {
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #ffffff;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 26rpx;
  opacity: 0.8;
}

.shift-status {
  text-align: right;
}

/* 快捷操作 */
.quick-actions {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-link {
  font-size: 26rpx;
  color: #1989fa;
  font-weight: normal;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 12rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  position: relative;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  background: #ebedf0;
}

.action-item text {
  font-size: 24rpx;
  color: #646566;
  margin-top: 12rpx;
  text-align: center;
}

/* 待办事项 */
.todo-section {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.todo-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff976a;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.todo-desc {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 8rpx;
}

.todo-time {
  font-size: 22rpx;
  color: #c8c9cc;
}

.todo-status {
  margin-left: 20rpx;
}

/* 最近交班 */
.recent-shifts {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.shift-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.shift-item {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.shift-item:active {
  background: #ebedf0;
  transform: translateY(2rpx);
}

.shift-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.shift-type {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.shift-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.status-pending {
  color: #ff976a;
  background: #fff7e6;
}

.status-confirmed {
  color: #07c160;
  background: #f0f9ff;
}

.status-rejected {
  color: #ee0a24;
  background: #fff1f0;
}

.shift-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.shift-info {
  font-size: 26rpx;
  color: #646566;
}

.shift-info .label {
  color: #969799;
}

/* 统计概览 */
.stats-overview {
  margin: 20rpx 30rpx 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
  padding: 20rpx 12rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #646566;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #c8c9cc;
}

.empty-state text {
  display: block;
  margin-top: 16rpx;
  font-size: 26rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
