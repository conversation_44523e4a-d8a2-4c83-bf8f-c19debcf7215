// pages/index/index.js
const app = getApp()
const util = require('../../utils/util.js')
const api = require('../../utils/api.js')
const config = require('../../utils/config.js')

Page({
  data: {
    userInfo: {},
    roleText: '',
    currentShift: null,
    unreadCount: 0,
    todoList: [],
    recentShifts: [],
    todayStats: {
      shiftCount: 0,
      handoverCount: 0,
      instrumentCount: 0,
      issueCount: 0
    },
    loading: false
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  async initPage() {
    try {
      // 检查登录状态
      if (!app.globalData.userInfo) {
        wx.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      this.setData({
        userInfo: app.globalData.userInfo,
        roleText: this.getRoleText(app.globalData.userRole)
      })

      await this.loadData()
    } catch (error) {
      console.error('页面初始化失败:', error)
      util.showToast('页面加载失败，请重试')
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadData()
    } catch (error) {
      console.error('数据刷新失败:', error)
      util.showToast('数据刷新失败')
    }
  },

  // 加载数据
  async loadData() {
    this.setData({ loading: true })

    try {
      const promises = [
        this.loadCurrentShift(),
        this.loadTodoList(),
        this.loadRecentShifts(),
        this.loadTodayStats(),
        this.loadUnreadCount()
      ]

      await Promise.all(promises)
    } catch (error) {
      console.error('数据加载失败:', error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载当前班次
  async loadCurrentShift() {
    try {
      const currentShift = await api.callFunction('getCurrentShift', {
        userId: app.globalData.userInfo.userId
      })
      
      this.setData({ currentShift })
      app.globalData.currentShift = currentShift
    } catch (error) {
      console.error('加载当前班次失败:', error)
    }
  },

  // 加载待办事项
  async loadTodoList() {
    try {
      const todoList = await api.callFunction('getTodoList', {
        userId: app.globalData.userInfo.userId,
        limit: 5
      })

      const formattedTodoList = todoList.map(item => ({
        ...item,
        createTime: util.getRelativeTime(item.createTime),
        priorityText: config.priorityDisplay[item.priority]?.text || '普通'
      }))

      this.setData({ todoList: formattedTodoList })
    } catch (error) {
      console.error('加载待办事项失败:', error)
    }
  },

  // 加载最近交班记录
  async loadRecentShifts() {
    try {
      const recentShifts = await api.getShiftList({
        limit: 3,
        orderBy: 'createTime',
        order: 'desc'
      })

      const formattedShifts = recentShifts.map(item => ({
        ...item,
        createTime: util.getRelativeTime(item.createTime),
        statusText: config.statusDisplay[item.status]?.text || '未知',
        instrumentCount: item.instrumentCheck?.length || 0
      }))

      this.setData({ recentShifts: formattedShifts })
    } catch (error) {
      console.error('加载最近交班记录失败:', error)
    }
  },

  // 加载今日统计
  async loadTodayStats() {
    try {
      const todayStats = await api.getStatistics({
        type: 'today',
        userId: app.globalData.userInfo.userId
      })

      this.setData({ todayStats })
    } catch (error) {
      console.error('加载今日统计失败:', error)
    }
  },

  // 加载未读消息数量
  async loadUnreadCount() {
    try {
      const unreadCount = await api.callFunction('getUnreadCount', {
        userId: app.globalData.userInfo.userId
      })

      this.setData({ unreadCount })
    } catch (error) {
      console.error('加载未读消息数量失败:', error)
    }
  },

  // 获取角色文本
  getRoleText(role) {
    const roleMap = {
      'nurse': '护士',
      'head_nurse': '护士长',
      'admin': '管理员'
    }
    return roleMap[role] || '护士'
  },

  // 新建交班
  createShift() {
    // 检查权限
    if (!app.checkPermission('nurse')) {
      util.showToast('您没有权限执行此操作')
      return
    }

    // 检查是否已有进行中的交班
    if (this.data.currentShift && this.data.currentShift.status === 'pending') {
      util.showConfirm('您有一个进行中的交班，是否继续编辑？').then(confirm => {
        if (confirm) {
          util.navigateTo('/pages/shift/create/create', {
            id: this.data.currentShift.id
          })
        }
      })
      return
    }

    util.navigateTo('/pages/shift/create/create')
  },

  // 扫码接班
  scanHandover() {
    // 检查权限
    if (!app.checkPermission('nurse')) {
      util.showToast('您没有权限执行此操作')
      return
    }

    util.navigateTo('/pages/handover/scan/scan')
  },

  // 查看交班记录
  viewShiftList() {
    wx.switchTab({
      url: '/pages/shift/list/list'
    })
  },

  // 查看消息中心
  viewMessages() {
    wx.switchTab({
      url: '/pages/message/message'
    })
  },

  // 查看交班详情
  viewShiftDetail(e) {
    const { id } = e.currentTarget.dataset
    util.navigateTo('/pages/shift/detail/detail', { id })
  },

  // 处理待办事项
  handleTodoItem(e) {
    const { item } = e.currentTarget.dataset
    
    switch (item.type) {
      case 'shift_pending':
        util.navigateTo('/pages/shift/detail/detail', { id: item.relatedId })
        break
      case 'handover_request':
        util.navigateTo('/pages/handover/confirm/confirm', { id: item.relatedId })
        break
      case 'system_notice':
        util.navigateTo('/pages/message/message')
        break
      default:
        util.showToast('功能开发中')
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '供应室交班系统',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '供应室交班系统 - 让交班更高效',
      imageUrl: '/images/share-cover.png'
    }
  }
})
