<!--pages/index/index.wxml-->
<view class="container">
  <!-- 顶部用户信息 -->
  <view class="user-header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="user-details">
        <view class="user-name">{{userInfo.name || '未登录'}}</view>
        <view class="user-role">{{roleText}} · {{userInfo.department || ''}}</view>
      </view>
    </view>
    <view class="shift-status">
      <van-tag wx:if="{{currentShift}}" type="primary" size="medium">
        {{currentShift.type}}班进行中
      </van-tag>
      <van-tag wx:else type="default" size="medium">未在班</van-tag>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="section-title">快捷操作</view>
    <view class="action-grid">
      <view class="action-item" bindtap="createShift">
        <van-icon name="add-o" size="48rpx" color="#1989fa" />
        <text>新建交班</text>
      </view>
      <view class="action-item" bindtap="scanHandover">
        <van-icon name="scan" size="48rpx" color="#07c160" />
        <text>扫码接班</text>
      </view>
      <view class="action-item" bindtap="viewShiftList">
        <van-icon name="orders-o" size="48rpx" color="#ff976a" />
        <text>交班记录</text>
      </view>
      <view class="action-item" bindtap="viewMessages">
        <van-icon name="chat-o" size="48rpx" color="#ee0a24" />
        <text>消息中心</text>
        <van-badge wx:if="{{unreadCount > 0}}" content="{{unreadCount}}" />
      </view>
    </view>
  </view>

  <!-- 待办事项 -->
  <view class="todo-section">
    <view class="section-title">
      <text>待办事项</text>
      <van-tag wx:if="{{todoList.length > 0}}" type="warning" size="small">{{todoList.length}}</van-tag>
    </view>
    <view wx:if="{{todoList.length > 0}}" class="todo-list">
      <view wx:for="{{todoList}}" wx:key="id" class="todo-item" bindtap="handleTodoItem" data-item="{{item}}">
        <view class="todo-content">
          <view class="todo-title">{{item.title}}</view>
          <view class="todo-desc">{{item.description}}</view>
          <view class="todo-time">{{item.createTime}}</view>
        </view>
        <view class="todo-status">
          <van-tag type="{{item.priority === 'urgent' ? 'danger' : 'warning'}}" size="small">
            {{item.priorityText}}
          </van-tag>
        </view>
      </view>
    </view>
    <view wx:else class="empty-state">
      <van-icon name="completed" size="80rpx" color="#c8c9cc" />
      <text>暂无待办事项</text>
    </view>
  </view>

  <!-- 最近交班 -->
  <view class="recent-shifts">
    <view class="section-title">
      <text>最近交班</text>
      <text class="more-link" bindtap="viewShiftList">查看更多</text>
    </view>
    <view wx:if="{{recentShifts.length > 0}}" class="shift-list">
      <view wx:for="{{recentShifts}}" wx:key="id" class="shift-item" bindtap="viewShiftDetail" data-id="{{item.id}}">
        <view class="shift-header">
          <view class="shift-type">{{item.type}}班</view>
          <view class="shift-status status-{{item.status}}">{{item.statusText}}</view>
        </view>
        <view class="shift-content">
          <view class="shift-info">
            <text class="label">交班人：</text>
            <text>{{item.creatorName}}</text>
          </view>
          <view class="shift-info">
            <text class="label">时间：</text>
            <text>{{item.createTime}}</text>
          </view>
          <view wx:if="{{item.instrumentCount}}" class="shift-info">
            <text class="label">器械：</text>
            <text>{{item.instrumentCount}}项</text>
          </view>
        </view>
      </view>
    </view>
    <view wx:else class="empty-state">
      <van-icon name="orders-o" size="80rpx" color="#c8c9cc" />
      <text>暂无交班记录</text>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="section-title">今日概览</view>
    <view class="stats-grid">
      <view class="stats-item">
        <view class="stats-number">{{todayStats.shiftCount || 0}}</view>
        <view class="stats-label">交班次数</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{todayStats.handoverCount || 0}}</view>
        <view class="stats-label">交接次数</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{todayStats.instrumentCount || 0}}</view>
        <view class="stats-label">器械清点</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{todayStats.issueCount || 0}}</view>
        <view class="stats-label">异常事项</view>
      </view>
    </view>
  </view>
</view>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Notify组件 -->
<van-notify id="van-notify" />
