<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 顶部装饰 -->
  <view class="login-header">
    <view class="logo-section">
      <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
      <view class="app-name">供应室交班系统</view>
      <view class="app-desc">专业的医疗器械交班管理平台</view>
    </view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-card">
      <view class="form-title">欢迎使用</view>
      
      <!-- 微信登录 -->
      <view class="login-section">
        <van-button 
          type="primary" 
          size="large" 
          round
          loading="{{wxLoginLoading}}"
          disabled="{{wxLoginLoading}}"
          open-type="getUserProfile"
          bindgetuserprofile="handleWxLogin"
          class="wx-login-btn">
          <van-icon name="wechat" size="20px" />
          <text>微信快速登录</text>
        </van-button>
        <view class="login-tips">
          <van-icon name="info-o" size="14px" />
          <text>首次登录需要绑定手机号</text>
        </view>
      </view>

      <!-- 手机号登录 -->
      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">或</text>
        <view class="divider-line"></view>
      </view>

      <view class="phone-login-section">
        <van-field
          value="{{phone}}"
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
          border="{{false}}"
          bind:change="onPhoneChange"
          custom-style="background: #f8f9fa; border-radius: 12rpx; margin-bottom: 20rpx;"
        >
          <van-icon slot="left-icon" name="phone-o" />
        </van-field>

        <view class="code-input-row">
          <van-field
            value="{{verifyCode}}"
            placeholder="请输入验证码"
            type="number"
            maxlength="6"
            border="{{false}}"
            bind:change="onCodeChange"
            custom-style="background: #f8f9fa; border-radius: 12rpx; flex: 1;"
          >
            <van-icon slot="left-icon" name="shield-o" />
          </van-field>
          <van-button 
            size="small" 
            type="{{canSendCode ? 'primary' : 'default'}}"
            disabled="{{!canSendCode || sendCodeLoading}}"
            loading="{{sendCodeLoading}}"
            bindtap="sendVerifyCode"
            class="send-code-btn">
            {{codeButtonText}}
          </van-button>
        </view>

        <van-button 
          type="primary" 
          size="large" 
          round
          loading="{{phoneLoginLoading}}"
          disabled="{{!canPhoneLogin || phoneLoginLoading}}"
          bindtap="handlePhoneLogin"
          class="phone-login-btn">
          手机号登录
        </van-button>
      </view>
    </view>

    <!-- 用户协议 -->
    <view class="agreement-section">
      <van-checkbox 
        value="{{agreedToTerms}}" 
        bind:change="onAgreementChange"
        icon-size="16px">
        <text class="agreement-text">
          我已阅读并同意
          <text class="link-text" bindtap="viewUserAgreement">《用户协议》</text>
          和
          <text class="link-text" bindtap="viewPrivacyPolicy">《隐私政策》</text>
        </text>
      </van-checkbox>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="login-footer">
    <view class="version-info">版本 {{version}}</view>
    <view class="support-info">
      <text>技术支持：</text>
      <text class="support-contact" bindtap="contactSupport">联系客服</text>
    </view>
  </view>
</view>

<!-- 弹窗组件 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />

<!-- 用户协议弹窗 -->
<van-popup 
  show="{{showAgreementPopup}}" 
  position="bottom" 
  round
  bind:close="closeAgreementPopup"
  custom-style="height: 70%;">
  <view class="agreement-popup">
    <view class="popup-header">
      <text class="popup-title">{{agreementTitle}}</text>
      <van-icon name="cross" bindtap="closeAgreementPopup" />
    </view>
    <scroll-view class="agreement-content" scroll-y>
      <rich-text nodes="{{agreementContent}}"></rich-text>
    </scroll-view>
  </view>
</van-popup>
