// app.js
App({
  globalData: {
    userInfo: null,
    openid: null,
    userRole: null, // nurse, head_nurse, admin
    currentShift: null,
    systemConfig: {
      version: '1.0.0',
      env: 'production' // development, production
    }
  },

  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'cloud1-7ggxhljbcb4191f2', // 云开发环境ID
        traceUser: true,
      })
    }

    // 检查登录状态
    this.checkLoginStatus()
    
    // 初始化Vant组件
    this.initVantComponents()
  },

  onShow() {
    // 应用显示时检查网络状态
    this.checkNetworkStatus()
  },

  onHide() {
    // 应用隐藏时保存数据
    this.saveAppData()
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    const openid = wx.getStorageSync('openid')
    
    if (userInfo && openid) {
      this.globalData.userInfo = userInfo
      this.globalData.openid = openid
      this.globalData.userRole = userInfo.role
    } else {
      // 跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })
    }
  },

  // 初始化Vant组件
  initVantComponents() {
    // 设置全局Toast配置
    const Toast = require('@vant/weapp/toast/toast')
    this.Toast = Toast
    
    // 设置全局Dialog配置
    const Dialog = require('@vant/weapp/dialog/dialog')
    this.Dialog = Dialog
    
    // 设置全局Notify配置
    const Notify = require('@vant/weapp/notify/notify')
    this.Notify = Notify
  },

  // 检查网络状态
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        if (res.networkType === 'none') {
          this.showToast('网络连接异常，请检查网络设置', 'error')
        }
      }
    })
  },

  // 保存应用数据
  saveAppData() {
    try {
      if (this.globalData.userInfo) {
        wx.setStorageSync('userInfo', this.globalData.userInfo)
      }
      if (this.globalData.openid) {
        wx.setStorageSync('openid', this.globalData.openid)
      }
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  },

  // 全局Toast提示
  showToast(message, type = 'success') {
    if (this.Toast) {
      this.Toast({
        type: type,
        message: message,
        duration: 2000
      })
    } else {
      wx.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 2000
      })
    }
  },

  // 全局Loading
  showLoading(message = '加载中...') {
    wx.showLoading({
      title: message,
      mask: true
    })
  },

  hideLoading() {
    wx.hideLoading()
  },

  // 全局错误处理
  handleError(error, showToast = true) {
    console.error('应用错误:', error)
    
    if (showToast) {
      let message = '操作失败，请重试'
      if (error && error.message) {
        message = error.message
      }
      this.showToast(message, 'error')
    }
    
    // 上报错误日志
    this.reportError(error)
  },

  // 上报错误日志
  reportError(error) {
    try {
      wx.cloud.callFunction({
        name: 'reportError',
        data: {
          error: error.toString(),
          stack: error.stack,
          userInfo: this.globalData.userInfo,
          timestamp: new Date().toISOString(),
          page: getCurrentPages().pop().route
        }
      })
    } catch (e) {
      console.error('上报错误失败:', e)
    }
  },

  // 获取用户权限
  getUserRole() {
    return this.globalData.userRole || 'nurse'
  },

  // 检查用户权限
  checkPermission(requiredRole) {
    const roleLevel = {
      'nurse': 1,
      'head_nurse': 2,
      'admin': 3
    }
    
    const userLevel = roleLevel[this.getUserRole()] || 1
    const requiredLevel = roleLevel[requiredRole] || 1
    
    return userLevel >= requiredLevel
  }
})
