// 云函数：获取交班详情
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { shiftId } = event

  try {
    if (!shiftId) {
      return {
        success: false,
        message: '交班ID不能为空'
      }
    }

    // 查询交班详情
    const result = await db.collection('shift_records')
      .doc(shiftId)
      .get()

    if (!result.data) {
      return {
        success: false,
        message: '交班记录不存在'
      }
    }

    const shiftDetail = result.data

    // 格式化数据
    const formattedDetail = {
      _id: shiftDetail._id,
      creator: shiftDetail.creator,
      creatorName: shiftDetail.creatorName,
      department: shiftDetail.department,
      shiftType: shiftDetail.shiftType,
      shiftDate: shiftDetail.shiftDate,
      priority: shiftDetail.priority,
      status: shiftDetail.status,
      instrumentCheck: shiftDetail.instrumentCheck || [],
      equipmentStatus: shiftDetail.equipmentStatus || [],
      specialNotes: shiftDetail.specialNotes || '',
      attachments: shiftDetail.attachments || [],
      createTime: shiftDetail.createTime,
      updateTime: shiftDetail.updateTime,
      handoverTime: shiftDetail.handoverTime,
      handoverUser: shiftDetail.handoverUser,
      handoverUserName: shiftDetail.handoverUserName,
      qrCode: shiftDetail.qrCode,
      signature: shiftDetail.signature
    }

    return {
      success: true,
      data: formattedDetail
    }
  } catch (error) {
    console.error('获取交班详情失败:', error)
    return {
      success: false,
      message: '获取交班详情失败',
      error: error.message
    }
  }
}
