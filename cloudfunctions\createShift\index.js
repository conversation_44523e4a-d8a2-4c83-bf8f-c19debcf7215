// 云函数：创建交班记录
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { 
    type, 
    instrumentCheck, 
    specialNotes, 
    equipmentStatus, 
    attachments,
    priority = 'normal'
  } = event
  
  const wxContext = cloud.getWXContext()
  const { openid } = wxContext

  try {
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()

    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userQuery.data[0]

    // 检查用户权限
    if (!['nurse', 'head_nurse', 'admin'].includes(user.role)) {
      return {
        success: false,
        message: '您没有权限创建交班记录'
      }
    }

    // 生成交班单号
    const now = new Date()
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
    const timeStr = now.toTimeString().slice(0, 2)
    const randomStr = Math.random().toString(36).substr(2, 6).toUpperCase()
    const shiftId = `S${dateStr}${timeStr}${randomStr}`

    // 创建交班记录
    const shiftData = {
      shiftId: shiftId,
      type: type,
      creator: user._id,
      creatorName: user.name || user.nickName,
      department: user.department,
      instrumentCheck: instrumentCheck || [],
      specialNotes: specialNotes || '',
      equipmentStatus: equipmentStatus || [],
      attachments: attachments || [],
      priority: priority,
      status: 'pending',
      createTime: now,
      updateTime: now,
      // 统计信息
      totalInstruments: (instrumentCheck || []).length,
      issueCount: (instrumentCheck || []).filter(item => item.actual !== item.plan).length,
      // 二维码信息
      qrCode: `shift:${shiftId}`,
      // 有效期（24小时）
      expireTime: new Date(now.getTime() + 24 * 60 * 60 * 1000)
    }

    const result = await db.collection('shift_records').add({
      data: shiftData
    })

    // 记录操作日志
    await db.collection('system_logs').add({
      data: {
        type: 'shift_operation',
        userId: user._id,
        action: 'create_shift',
        shiftId: shiftId,
        details: {
          type: type,
          instrumentCount: (instrumentCheck || []).length,
          priority: priority
        },
        timestamp: now
      }
    })

    // 发送通知给相关人员
    if (user.department) {
      await this.sendShiftNotification(user.department, shiftId, 'shift_created', user)
    }

    return {
      success: true,
      data: {
        shiftId: shiftId,
        recordId: result._id,
        qrCode: `shift:${shiftId}`,
        createTime: now
      },
      message: '交班记录创建成功'
    }
  } catch (error) {
    console.error('创建交班记录失败:', error)
    return {
      success: false,
      message: '创建交班记录失败，请重试',
      error: error.message
    }
  }
}

// 发送交班通知
async function sendShiftNotification(department, shiftId, type, creator) {
  try {
    // 获取同部门的其他用户
    const usersQuery = await db.collection('users').where({
      department: department,
      status: 'active',
      _id: _.neq(creator._id)
    }).get()

    const notifications = usersQuery.data.map(user => ({
      userId: user._id,
      type: type,
      title: '新的交班记录',
      content: `${creator.name || creator.nickName}创建了新的交班记录`,
      relatedId: shiftId,
      priority: 'normal',
      isRead: false,
      createTime: new Date()
    }))

    if (notifications.length > 0) {
      await db.collection('notifications').add({
        data: notifications
      })
    }
  } catch (error) {
    console.error('发送通知失败:', error)
  }
}
