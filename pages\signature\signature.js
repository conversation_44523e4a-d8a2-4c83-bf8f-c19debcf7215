// pages/signature/signature.js
const app = getApp()
const util = require('../../utils/util.js')
const api = require('../../utils/api.js')

Page({
  data: {
    // 签名状态
    hasSignature: false,
    currentSignature: '',
    signatureCreateTime: '',
    signatureUsageCount: 0,
    
    // 画板状态
    showSignatureBoard: false,
    isUpdate: false,
    
    // 画笔设置
    brushSize: 4,
    brushColor: '#000000',
    
    // 绘制状态
    hasDrawn: false,
    isDrawing: false,
    lastPoint: null,
    
    // 预览状态
    showPreview: false,
    
    // 签名历史
    signatureHistory: [],
    hasMoreHistory: false,
    loadingHistory: false,
    
    // 加载状态
    saveLoading: false
  },

  onLoad() {
    this.initPage()
  },

  onReady() {
    // 获取画布上下文
    this.ctx = wx.createCanvasContext('signatureCanvas')
    this.setupCanvas()
  },

  onShow() {
    this.loadSignatureData()
  },

  // 初始化页面
  initPage() {
    // 获取系统信息，设置画布尺寸
    wx.getSystemInfo({
      success: (res) => {
        this.canvasWidth = res.windowWidth - 80 // 减去padding
        this.canvasHeight = 400 * res.pixelRatio / 2 // rpx转px
      }
    })
  },

  // 设置画布
  setupCanvas() {
    if (!this.ctx) return
    
    this.ctx.setLineCap('round')
    this.ctx.setLineJoin('round')
    this.ctx.setLineWidth(this.data.brushSize)
    this.ctx.setStrokeStyle(this.data.brushColor)
  },

  // 加载签名数据
  async loadSignatureData() {
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo) return

      const result = await api.callFunction('getUserSignature', {
        userId: userInfo.userId
      })

      if (result.success && result.data) {
        this.setData({
          hasSignature: true,
          currentSignature: result.data.signatureUrl,
          signatureCreateTime: util.formatTime(new Date(result.data.createTime)),
          signatureUsageCount: result.data.usageCount || 0
        })
        
        // 加载使用历史
        this.loadSignatureHistory()
      } else {
        this.setData({ hasSignature: false })
      }
    } catch (error) {
      console.error('加载签名数据失败:', error)
      util.showToast('加载签名数据失败')
    }
  },

  // 加载签名历史
  async loadSignatureHistory() {
    try {
      const userInfo = app.globalData.userInfo
      const result = await api.callFunction('getSignatureHistory', {
        userId: userInfo.userId,
        page: 1,
        limit: 10
      })

      if (result.success) {
        const history = result.data.list.map(item => ({
          id: item._id,
          title: item.title || '交班确认',
          time: util.formatTime(new Date(item.timestamp)),
          status: item.status,
          statusText: item.status === 'success' ? '成功' : '失败'
        }))

        this.setData({
          signatureHistory: history,
          hasMoreHistory: result.data.hasMore
        })
      }
    } catch (error) {
      console.error('加载签名历史失败:', error)
    }
  },

  // 创建签名
  createSignature() {
    this.setData({
      showSignatureBoard: true,
      isUpdate: false
    })
    this.clearCanvas()
  },

  // 更新签名
  updateSignature() {
    this.setData({
      showSignatureBoard: true,
      isUpdate: true
    })
    this.clearCanvas()
  },

  // 删除签名
  async deleteSignature() {
    try {
      const result = await util.showModal('确认删除', '删除后将无法恢复，确定要删除当前签名吗？')
      if (!result.confirm) return

      util.showLoading('删除中...')

      const userInfo = app.globalData.userInfo
      const deleteResult = await api.callFunction('deleteUserSignature', {
        userId: userInfo.userId
      })

      if (deleteResult.success) {
        util.showToast('签名已删除', 'success')
        this.setData({
          hasSignature: false,
          currentSignature: '',
          signatureHistory: []
        })
      } else {
        throw new Error(deleteResult.message)
      }
    } catch (error) {
      console.error('删除签名失败:', error)
      util.showToast('删除失败，请重试')
    } finally {
      util.hideLoading()
    }
  },

  // 预览签名
  previewSignature() {
    this.setData({ showPreview: true })
  },

  // 关闭预览
  closePreview() {
    this.setData({ showPreview: false })
  },

  // 画笔大小变化
  onBrushSizeChange(e) {
    const brushSize = e.detail.value
    this.setData({ brushSize })
    if (this.ctx) {
      this.ctx.setLineWidth(brushSize)
    }
  },

  // 选择颜色
  selectColor(e) {
    const color = e.currentTarget.dataset.color
    this.setData({ brushColor: color })
    if (this.ctx) {
      this.ctx.setStrokeStyle(color)
    }
  },

  // 触摸开始
  onTouchStart(e) {
    if (!this.ctx) return
    
    const touch = e.touches[0]
    const point = {
      x: touch.x,
      y: touch.y
    }
    
    this.setData({ 
      isDrawing: true,
      lastPoint: point,
      hasDrawn: true
    })
    
    this.ctx.beginPath()
    this.ctx.moveTo(point.x, point.y)
  },

  // 触摸移动
  onTouchMove(e) {
    if (!this.data.isDrawing || !this.ctx) return
    
    const touch = e.touches[0]
    const point = {
      x: touch.x,
      y: touch.y
    }
    
    this.ctx.lineTo(point.x, point.y)
    this.ctx.stroke()
    this.ctx.draw(true)
    
    this.setData({ lastPoint: point })
  },

  // 触摸结束
  onTouchEnd() {
    this.setData({ isDrawing: false })
  },

  // 清空画布
  clearCanvas() {
    if (!this.ctx) return
    
    this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
    this.ctx.draw()
    this.setData({ hasDrawn: false })
  },

  // 取消签名
  cancelSignature() {
    this.setData({ showSignatureBoard: false })
  },

  // 保存签名
  async saveSignature() {
    try {
      if (!this.data.hasDrawn) {
        util.showToast('请先绘制签名')
        return
      }

      this.setData({ saveLoading: true })

      // 将画布内容转为图片
      const tempFilePath = await this.canvasToImage()
      
      // 上传签名图片
      const uploadResult = await api.uploadFile(tempFilePath, 'signatures/')
      if (!uploadResult.success) {
        throw new Error('上传签名图片失败')
      }

      // 保存签名信息
      const userInfo = app.globalData.userInfo
      const saveResult = await api.callFunction('saveUserSignature', {
        userId: userInfo.userId,
        signatureUrl: uploadResult.fileID,
        isUpdate: this.data.isUpdate
      })

      if (saveResult.success) {
        util.showToast('签名保存成功', 'success')
        this.setData({ showSignatureBoard: false })
        this.loadSignatureData()
      } else {
        throw new Error(saveResult.message)
      }
    } catch (error) {
      console.error('保存签名失败:', error)
      util.showToast('保存失败，请重试')
    } finally {
      this.setData({ saveLoading: false })
    }
  },

  // 画布转图片
  canvasToImage() {
    return new Promise((resolve, reject) => {
      wx.canvasToTempFilePath({
        canvasId: 'signatureCanvas',
        success: (res) => resolve(res.tempFilePath),
        fail: reject
      })
    })
  },

  // 加载更多历史
  async loadMoreHistory() {
    if (this.data.loadingHistory) return

    try {
      this.setData({ loadingHistory: true })

      const userInfo = app.globalData.userInfo
      const currentPage = Math.floor(this.data.signatureHistory.length / 10) + 1
      
      const result = await api.callFunction('getSignatureHistory', {
        userId: userInfo.userId,
        page: currentPage + 1,
        limit: 10
      })

      if (result.success) {
        const newHistory = result.data.list.map(item => ({
          id: item._id,
          title: item.title || '交班确认',
          time: util.formatTime(new Date(item.timestamp)),
          status: item.status,
          statusText: item.status === 'success' ? '成功' : '失败'
        }))

        this.setData({
          signatureHistory: [...this.data.signatureHistory, ...newHistory],
          hasMoreHistory: result.data.hasMore
        })
      }
    } catch (error) {
      console.error('加载更多历史失败:', error)
      util.showToast('加载失败')
    } finally {
      this.setData({ loadingHistory: false })
    }
  }
})
