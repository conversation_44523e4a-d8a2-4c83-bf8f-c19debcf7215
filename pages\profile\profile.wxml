<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-header">
      <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill" bindtap="changeAvatar"></image>
      <view class="user-details">
        <view class="user-name">{{userInfo.name || userInfo.nickName || '未设置'}}</view>
        <view class="user-role">
          <van-tag type="primary" size="medium">{{roleText}}</van-tag>
          <text class="department">{{userInfo.department || '未设置部门'}}</text>
        </view>
        <view class="user-phone">{{maskedPhone}}</view>
      </view>
      <van-icon name="edit" size="20px" color="#1989fa" bindtap="editProfile" />
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-row">
      <view class="stat-item">
        <view class="stat-number">{{userStats.shiftCount || 0}}</view>
        <view class="stat-label">交班次数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userStats.handoverCount || 0}}</view>
        <view class="stat-label">接班次数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userStats.signatureCount || 0}}</view>
        <view class="stat-label">签名次数</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <van-cell-group>
      <van-cell 
        title="个人信息" 
        icon="contact" 
        is-link 
        bindtap="editProfile"
        value="{{userInfo.name ? '已完善' : '待完善'}}"
        label="姓名、部门、联系方式等"
      />
      <van-cell 
        title="电子签名" 
        icon="edit" 
        is-link 
        bindtap="manageSignature"
        value="{{hasSignature ? '已设置' : '未设置'}}"
        label="用于交班确认的电子签名"
      />
      <van-cell 
        title="安全设置" 
        icon="shield-o" 
        is-link 
        bindtap="securitySettings"
        label="密码、权限、登录记录等"
      />
    </van-cell-group>
  </view>

  <!-- 工作记录 -->
  <view class="work-section">
    <view class="section-title">工作记录</view>
    <van-cell-group>
      <van-cell 
        title="我的交班" 
        icon="orders-o" 
        is-link 
        bindtap="viewMyShifts"
        value="{{recentShiftCount}}条"
        label="查看我创建的交班记录"
      />
      <van-cell 
        title="我的接班" 
        icon="completed" 
        is-link 
        bindtap="viewMyHandovers"
        value="{{recentHandoverCount}}条"
        label="查看我确认的接班记录"
      />
      <van-cell 
        title="操作日志" 
        icon="records" 
        is-link 
        bindtap="viewOperationLogs"
        label="查看详细的操作记录"
      />
    </van-cell-group>
  </view>

  <!-- 系统设置 -->
  <view class="system-section">
    <view class="section-title">系统设置</view>
    <van-cell-group>
      <van-cell 
        title="消息通知" 
        icon="bell" 
        is-link 
        bindtap="notificationSettings"
        label="管理消息推送设置"
      />
      <van-cell 
        title="帮助中心" 
        icon="question-o" 
        is-link 
        bindtap="helpCenter"
        label="使用指南、常见问题"
      />
      <van-cell 
        title="关于我们" 
        icon="info-o" 
        is-link 
        bindtap="aboutUs"
        value="v{{version}}"
        label="版本信息、联系方式"
      />
    </van-cell-group>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <van-button 
      type="danger" 
      size="large" 
      round 
      bindtap="logout"
      loading="{{logoutLoading}}"
      disabled="{{logoutLoading}}">
      退出登录
    </van-button>
  </view>
</view>

<!-- 编辑个人信息弹窗 -->
<van-popup 
  show="{{showEditPopup}}" 
  position="bottom" 
  round
  bind:close="closeEditPopup"
  custom-style="height: 60%;">
  <view class="edit-popup">
    <view class="popup-header">
      <text class="popup-title">编辑个人信息</text>
      <van-icon name="cross" bindtap="closeEditPopup" />
    </view>
    <view class="edit-form">
      <van-field
        value="{{editForm.name}}"
        label="真实姓名"
        placeholder="请输入真实姓名"
        required
        bind:change="onEditFormChange"
        data-field="name"
      />
      <van-field
        value="{{editForm.phone}}"
        label="手机号码"
        placeholder="请输入手机号码"
        type="number"
        maxlength="11"
        required
        bind:change="onEditFormChange"
        data-field="phone"
      />
      <van-field
        value="{{editForm.department}}"
        label="所属部门"
        placeholder="请选择部门"
        readonly
        is-link
        bind:click-input="selectDepartment"
      />
      <van-field
        value="{{editForm.jobNumber}}"
        label="工号"
        placeholder="请输入工号"
        bind:change="onEditFormChange"
        data-field="jobNumber"
      />
    </view>
    <view class="popup-footer">
      <van-button type="default" size="large" bindtap="closeEditPopup">取消</van-button>
      <van-button type="primary" size="large" bindtap="saveProfile" loading="{{saveLoading}}">保存</van-button>
    </view>
  </view>
</van-popup>

<!-- 部门选择弹窗 -->
<van-popup 
  show="{{showDepartmentPopup}}" 
  position="bottom" 
  round
  bind:close="closeDepartmentPopup">
  <van-picker
    columns="{{departmentColumns}}"
    bind:confirm="onDepartmentConfirm"
    bind:cancel="closeDepartmentPopup"
    title="选择部门"
  />
</van-popup>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Dialog组件 -->
<van-dialog id="van-dialog" />
