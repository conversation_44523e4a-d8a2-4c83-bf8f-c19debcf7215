// pages/login/login.js
const app = getApp()
const util = require('../../utils/util.js')
const api = require('../../utils/api.js')
const config = require('../../utils/config.js')

Page({
  data: {
    // 表单数据
    phone: '',
    verifyCode: '',
    agreedToTerms: false,
    
    // 加载状态
    wxLoginLoading: false,
    phoneLoginLoading: false,
    sendCodeLoading: false,
    
    // 验证码相关
    canSendCode: false,
    codeButtonText: '获取验证码',
    countdown: 0,
    countdownTimer: null,
    
    // 弹窗相关
    showAgreementPopup: false,
    agreementTitle: '',
    agreementContent: '',
    
    // 系统信息
    version: '1.0.0'
  },

  onLoad() {
    this.initPage()
  },

  onUnload() {
    // 清理定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer)
    }
  },

  // 初始化页面
  initPage() {
    // 检查是否已登录
    if (app.globalData.userInfo && app.globalData.openid) {
      wx.reLaunch({
        url: '/pages/index/index'
      })
      return
    }

    // 获取系统信息
    this.setData({
      version: app.globalData.systemConfig.version
    })
  },

  // 手机号输入
  onPhoneChange(e) {
    const phone = e.detail
    this.setData({
      phone,
      canSendCode: util.validatePhone(phone)
    })
  },

  // 验证码输入
  onCodeChange(e) {
    this.setData({
      verifyCode: e.detail
    })
  },

  // 协议勾选
  onAgreementChange(e) {
    this.setData({
      agreedToTerms: e.detail
    })
  },

  // 微信登录
  async handleWxLogin(e) {
    try {
      // 检查协议
      if (!this.data.agreedToTerms) {
        util.showToast('请先同意用户协议和隐私政策')
        return
      }

      this.setData({ wxLoginLoading: true })

      // 获取用户信息
      const { userInfo } = e.detail
      if (!userInfo) {
        util.showToast('获取用户信息失败')
        return
      }

      // 获取登录凭证
      const loginRes = await this.wxLogin()
      if (!loginRes.code) {
        throw new Error('获取登录凭证失败')
      }

      // 调用登录接口
      const loginResult = await api.login(loginRes.code, userInfo)
      
      if (loginResult.success) {
        // 保存用户信息
        app.globalData.userInfo = loginResult.userInfo
        app.globalData.openid = loginResult.openid
        app.globalData.userRole = loginResult.userInfo.role

        util.setStorage('userInfo', loginResult.userInfo)
        util.setStorage('openid', loginResult.openid)

        util.showToast('登录成功', 'success')
        
        // 跳转到首页
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)
      } else {
        throw new Error(loginResult.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      util.showToast(error.message || '登录失败，请重试')
    } finally {
      this.setData({ wxLoginLoading: false })
    }
  },

  // 微信登录授权
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  // 发送验证码
  async sendVerifyCode() {
    try {
      if (!this.data.canSendCode) {
        util.showToast('请输入正确的手机号')
        return
      }

      this.setData({ sendCodeLoading: true })

      // 调用发送验证码接口
      const result = await api.callFunction('sendVerifyCode', {
        phone: this.data.phone
      })

      if (result.success) {
        util.showToast('验证码已发送', 'success')
        this.startCountdown()
      } else {
        throw new Error(result.message || '发送验证码失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      util.showToast(error.message || '发送验证码失败')
    } finally {
      this.setData({ sendCodeLoading: false })
    }
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown,
      canSendCode: false,
      codeButtonText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          canSendCode: util.validatePhone(this.data.phone),
          codeButtonText: '获取验证码',
          countdownTimer: null
        })
      } else {
        this.setData({
          countdown,
          codeButtonText: `${countdown}s后重发`
        })
      }
    }, 1000)

    this.setData({ countdownTimer: timer })
  },

  // 手机号登录
  async handlePhoneLogin() {
    try {
      // 检查协议
      if (!this.data.agreedToTerms) {
        util.showToast('请先同意用户协议和隐私政策')
        return
      }

      // 验证输入
      if (!util.validatePhone(this.data.phone)) {
        util.showToast('请输入正确的手机号')
        return
      }

      if (!this.data.verifyCode || this.data.verifyCode.length !== 6) {
        util.showToast('请输入6位验证码')
        return
      }

      this.setData({ phoneLoginLoading: true })

      // 调用手机号登录接口
      const result = await api.callFunction('phoneLogin', {
        phone: this.data.phone,
        verifyCode: this.data.verifyCode
      })

      if (result.success) {
        // 保存用户信息
        app.globalData.userInfo = result.userInfo
        app.globalData.openid = result.openid
        app.globalData.userRole = result.userInfo.role

        util.setStorage('userInfo', result.userInfo)
        util.setStorage('openid', result.openid)

        util.showToast('登录成功', 'success')
        
        // 跳转到首页
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)
      } else {
        throw new Error(result.message || '登录失败')
      }
    } catch (error) {
      console.error('手机号登录失败:', error)
      util.showToast(error.message || '登录失败，请重试')
    } finally {
      this.setData({ phoneLoginLoading: false })
    }
  },

  // 计算属性：是否可以手机号登录
  get canPhoneLogin() {
    return util.validatePhone(this.data.phone) && 
           this.data.verifyCode.length === 6 && 
           this.data.agreedToTerms
  },

  // 查看用户协议
  viewUserAgreement() {
    this.setData({
      showAgreementPopup: true,
      agreementTitle: '用户协议',
      agreementContent: this.getUserAgreementContent()
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    this.setData({
      showAgreementPopup: true,
      agreementTitle: '隐私政策',
      agreementContent: this.getPrivacyPolicyContent()
    })
  },

  // 关闭协议弹窗
  closeAgreementPopup() {
    this.setData({
      showAgreementPopup: false
    })
  },

  // 联系客服
  contactSupport() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        util.showToast('拨打电话失败')
      }
    })
  },

  // 获取用户协议内容
  getUserAgreementContent() {
    return `
      <h3>用户协议</h3>
      <p>欢迎使用供应室交班系统！</p>
      <p>本协议是您与我们之间关于使用本应用的法律协议。请您仔细阅读以下条款：</p>
      
      <h4>1. 服务说明</h4>
      <p>本应用为医疗机构供应室提供交班管理服务。</p>
      
      <h4>2. 用户义务</h4>
      <p>您在使用本服务时应当遵守相关法律法规。</p>
      
      <p>本协议的解释权归我们所有。如有疑问，请联系客服。</p>
    `
  },

  // 获取隐私政策内容
  getPrivacyPolicyContent() {
    return `
      <h3>隐私政策</h3>
      <p>我们非常重视您的隐私保护。</p>
      
      <h4>1. 信息收集</h4>
      <p>我们可能收集基本信息、使用信息、设备信息。</p>
      
      <h4>2. 信息保护</h4>
      <p>我们采取严格措施保护您的信息安全。</p>
      
      <p>如有隐私相关问题，请联系我们的客服。</p>
    `
  }
})
