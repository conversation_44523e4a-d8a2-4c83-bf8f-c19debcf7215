// 云函数：获取器械列表
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { category, searchKey, page = 1, limit = 50 } = event

  try {
    let query = db.collection('instruments')

    // 按分类筛选
    if (category) {
      query = query.where({
        category: category
      })
    }

    // 按关键词搜索
    if (searchKey) {
      const searchRegex = new RegExp(searchKey, 'i')
      query = query.where({
        $or: [
          { name: searchRegex },
          { code: searchRegex },
          { description: searchRegex }
        ]
      })
    }

    // 分页查询
    const skip = (page - 1) * limit
    const result = await query
      .orderBy('category', 'asc')
      .orderBy('name', 'asc')
      .skip(skip)
      .limit(limit)
      .get()

    // 获取总数
    const countResult = await query.count()

    return {
      success: true,
      data: result.data,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        hasMore: skip + result.data.length < countResult.total
      }
    }
  } catch (error) {
    console.error('获取器械列表失败:', error)
    return {
      success: false,
      message: '获取器械列表失败',
      error: error.message
    }
  }
}
