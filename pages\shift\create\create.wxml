<!--pages/shift/create/create.wxml-->
<view class="create-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="title">新建交班</view>
    <view class="subtitle">请填写交班信息</view>
  </view>

  <!-- 基本信息 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>
    <van-cell-group>
      <van-field
        label="班次类型"
        value="{{formData.shiftType}}"
        placeholder="请选择班次类型"
        readonly
        is-link
        bind:click-input="selectShiftType"
        required
      />
      <van-field
        label="交班日期"
        value="{{formData.shiftDate}}"
        placeholder="请选择交班日期"
        readonly
        is-link
        bind:click-input="selectShiftDate"
        required
      />
      <van-field
        label="优先级"
        value="{{priorityText}}"
        placeholder="请选择优先级"
        readonly
        is-link
        bind:click-input="selectPriority"
      />
    </van-cell-group>
  </view>

  <!-- 器械清点 -->
  <view class="form-section">
    <view class="section-title">
      <text>器械清点</text>
      <van-button type="primary" size="mini" bindtap="addInstrument">添加器械</van-button>
    </view>
    
    <view class="instrument-list" wx:if="{{formData.instrumentCheck.length > 0}}">
      <view class="instrument-item" wx:for="{{formData.instrumentCheck}}" wx:key="index">
        <view class="instrument-header">
          <view class="instrument-name">{{item.name}}</view>
          <van-icon name="delete-o" size="18px" color="#ee0a24" bindtap="removeInstrument" data-index="{{index}}" />
        </view>
        <view class="instrument-details">
          <view class="detail-row">
            <text class="label">计划数量：</text>
            <van-stepper 
              value="{{item.plan}}" 
              min="0" 
              bind:change="onInstrumentPlanChange"
              data-index="{{index}}"
            />
            <text class="unit">{{item.unit}}</text>
          </view>
          <view class="detail-row">
            <text class="label">实际数量：</text>
            <van-stepper 
              value="{{item.actual}}" 
              min="0" 
              bind:change="onInstrumentActualChange"
              data-index="{{index}}"
            />
            <text class="unit">{{item.unit}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.plan !== item.actual}}">
            <text class="label">差异原因：</text>
            <van-field
              value="{{item.reason}}"
              placeholder="请说明差异原因"
              bind:change="onInstrumentReasonChange"
              data-index="{{index}}"
              type="textarea"
              autosize
            />
          </view>
        </view>
        <view class="instrument-status">
          <van-tag type="{{item.plan === item.actual ? 'success' : 'warning'}}" size="medium">
            {{item.plan === item.actual ? '正常' : '异常'}}
          </van-tag>
        </view>
      </view>
    </view>
    
    <van-empty description="暂无器械清点记录" wx:if="{{formData.instrumentCheck.length === 0}}">
      <van-button type="primary" size="small" bindtap="addInstrument">添加器械</van-button>
    </van-empty>
  </view>

  <!-- 设备状态 -->
  <view class="form-section">
    <view class="section-title">
      <text>设备状态</text>
      <van-button type="primary" size="mini" bindtap="addEquipment">添加设备</van-button>
    </view>
    
    <view class="equipment-list" wx:if="{{formData.equipmentStatus.length > 0}}">
      <view class="equipment-item" wx:for="{{formData.equipmentStatus}}" wx:key="index">
        <view class="equipment-header">
          <view class="equipment-name">{{item.name}}</view>
          <van-icon name="delete-o" size="18px" color="#ee0a24" bindtap="removeEquipment" data-index="{{index}}" />
        </view>
        <view class="equipment-details">
          <van-field
            label="设备编号"
            value="{{item.code}}"
            placeholder="请输入设备编号"
            bind:change="onEquipmentCodeChange"
            data-index="{{index}}"
          />
          <van-field
            label="运行状态"
            value="{{item.status}}"
            placeholder="请选择运行状态"
            readonly
            is-link
            bind:click-input="selectEquipmentStatus"
            data-index="{{index}}"
          />
          <van-field
            label="备注说明"
            value="{{item.remark}}"
            placeholder="请输入备注说明"
            bind:change="onEquipmentRemarkChange"
            data-index="{{index}}"
            type="textarea"
            autosize
          />
        </view>
      </view>
    </view>
    
    <van-empty description="暂无设备状态记录" wx:if="{{formData.equipmentStatus.length === 0}}">
      <van-button type="primary" size="small" bindtap="addEquipment">添加设备</van-button>
    </van-empty>
  </view>

  <!-- 特殊事项 -->
  <view class="form-section">
    <view class="section-title">特殊事项</view>
    <van-field
      value="{{formData.specialNotes}}"
      placeholder="请输入特殊事项说明（如有）"
      bind:change="onSpecialNotesChange"
      type="textarea"
      autosize
      maxlength="500"
      show-word-limit
    />
  </view>

  <!-- 附件上传 -->
  <view class="form-section">
    <view class="section-title">
      <text>附件上传</text>
      <van-button type="primary" size="mini" bindtap="uploadAttachment">上传附件</van-button>
    </view>
    
    <view class="attachment-list" wx:if="{{formData.attachments.length > 0}}">
      <view class="attachment-item" wx:for="{{formData.attachments}}" wx:key="index">
        <image class="attachment-image" src="{{item.url}}" mode="aspectFill" bindtap="previewAttachment" data-index="{{index}}"></image>
        <view class="attachment-info">
          <text class="attachment-name">{{item.name}}</text>
          <text class="attachment-size">{{item.size}}</text>
        </view>
        <van-icon name="delete-o" size="18px" color="#ee0a24" bindtap="removeAttachment" data-index="{{index}}" />
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <van-button 
      type="primary" 
      size="large" 
      round 
      bindtap="submitShift"
      loading="{{submitLoading}}"
      disabled="{{!canSubmit || submitLoading}}">
      提交交班
    </van-button>
  </view>
</view>

<!-- 班次类型选择 -->
<van-popup 
  show="{{showShiftTypePopup}}" 
  position="bottom" 
  round
  bind:close="closeShiftTypePopup">
  <van-picker
    columns="{{shiftTypeColumns}}"
    bind:confirm="onShiftTypeConfirm"
    bind:cancel="closeShiftTypePopup"
    title="选择班次类型"
  />
</van-popup>

<!-- 日期选择 -->
<van-popup 
  show="{{showDatePopup}}" 
  position="bottom" 
  round
  bind:close="closeDatePopup">
  <van-datetime-picker
    type="date"
    value="{{currentDate}}"
    bind:confirm="onDateConfirm"
    bind:cancel="closeDatePopup"
    title="选择交班日期"
  />
</van-popup>

<!-- 优先级选择 -->
<van-popup 
  show="{{showPriorityPopup}}" 
  position="bottom" 
  round
  bind:close="closePriorityPopup">
  <van-picker
    columns="{{priorityColumns}}"
    bind:confirm="onPriorityConfirm"
    bind:cancel="closePriorityPopup"
    title="选择优先级"
  />
</van-popup>

<!-- 器械选择 -->
<van-popup 
  show="{{showInstrumentPopup}}" 
  position="bottom" 
  round
  bind:close="closeInstrumentPopup"
  custom-style="height: 60%;">
  <view class="instrument-popup">
    <view class="popup-header">
      <text class="popup-title">选择器械</text>
      <van-icon name="cross" bindtap="closeInstrumentPopup" />
    </view>
    <view class="instrument-search">
      <van-search 
        value="{{instrumentSearchKey}}" 
        placeholder="搜索器械名称" 
        bind:change="onInstrumentSearch"
      />
    </view>
    <view class="instrument-options">
      <view 
        class="instrument-option" 
        wx:for="{{filteredInstruments}}" 
        wx:key="code"
        bindtap="selectInstrument"
        data-instrument="{{item}}">
        <view class="option-name">{{item.name}}</view>
        <view class="option-info">
          <text class="option-code">{{item.code}}</text>
          <text class="option-unit">{{item.unit}}</text>
        </view>
      </view>
    </view>
  </view>
</van-popup>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Dialog组件 -->
<van-dialog id="van-dialog" />
