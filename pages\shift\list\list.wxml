<!--pages/shift/list/list.wxml-->
<view class="list-container">
  <!-- 搜索和筛选 -->
  <view class="search-section">
    <van-search 
      value="{{searchKey}}" 
      placeholder="搜索交班记录" 
      bind:search="onSearch"
      bind:change="onSearchChange"
      show-action
    >
      <view slot="action" bindtap="showFilter">
        <van-icon name="filter-o" size="18px" />
      </view>
    </van-search>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tags" wx:if="{{hasActiveFilters}}">
    <van-tag 
      wx:if="{{filterData.status}}" 
      closeable 
      type="primary" 
      size="medium"
      bind:close="clearStatusFilter">
      {{getStatusText(filterData.status)}}
    </van-tag>
    <van-tag 
      wx:if="{{filterData.shiftType}}" 
      closeable 
      type="primary" 
      size="medium"
      bind:close="clearShiftTypeFilter">
      {{filterData.shiftType}}
    </van-tag>
    <van-tag 
      wx:if="{{filterData.priority}}" 
      closeable 
      type="primary" 
      size="medium"
      bind:close="clearPriorityFilter">
      {{getPriorityText(filterData.priority)}}
    </van-tag>
    <van-button type="default" size="mini" bindtap="clearAllFilters">清空</van-button>
  </view>

  <!-- 交班列表 -->
  <view class="shift-list">
    <view class="shift-item" wx:for="{{shiftList}}" wx:key="_id" bindtap="viewShiftDetail" data-id="{{item._id}}">
      <!-- 头部信息 -->
      <view class="shift-header">
        <view class="shift-info">
          <view class="shift-title">{{item.shiftType}} - {{item.shiftDate}}</view>
          <view class="shift-creator">创建人：{{item.creatorName}}</view>
        </view>
        <view class="shift-status">
          <van-tag type="{{getStatusType(item.status)}}" size="medium">
            {{getStatusText(item.status)}}
          </van-tag>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="shift-details">
        <view class="detail-row">
          <van-icon name="clock-o" size="14px" color="#969799" />
          <text class="detail-text">创建时间：{{formatTime(item.createTime)}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.handoverTime}}">
          <van-icon name="checked" size="14px" color="#07c160" />
          <text class="detail-text">接班时间：{{formatTime(item.handoverTime)}}</text>
        </view>
        <view class="detail-row">
          <van-icon name="location-o" size="14px" color="#969799" />
          <text class="detail-text">科室：{{item.department}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.priority !== 'normal'}}">
          <van-icon name="warning-o" size="14px" color="{{getPriorityColor(item.priority)}}" />
          <text class="detail-text">优先级：{{getPriorityText(item.priority)}}</text>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="shift-stats">
        <view class="stat-item">
          <text class="stat-label">器械</text>
          <text class="stat-value">{{item.instrumentCount || 0}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">设备</text>
          <text class="stat-value">{{item.equipmentCount || 0}}</text>
        </view>
        <view class="stat-item" wx:if="{{item.issueCount > 0}}">
          <text class="stat-label">异常</text>
          <text class="stat-value error">{{item.issueCount}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="shift-actions" wx:if="{{item.status === 'pending'}}">
        <van-button 
          type="default" 
          size="mini" 
          bindtap="editShift" 
          data-id="{{item._id}}"
          catchtap="true">
          编辑
        </van-button>
        <van-button 
          type="primary" 
          size="mini" 
          bindtap="generateQRCode" 
          data-id="{{item._id}}"
          catchtap="true">
          生成二维码
        </van-button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <van-empty 
    description="{{searchKey ? '未找到相关交班记录' : '暂无交班记录'}}" 
    wx:if="{{shiftList.length === 0 && !loading}}">
    <van-button type="primary" size="small" bindtap="createShift" wx:if="{{!searchKey}}">
      新建交班
    </van-button>
  </van-empty>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && shiftList.length > 0}}">
    <van-button 
      type="default" 
      size="small" 
      loading="{{loadingMore}}" 
      bindtap="loadMore">
      {{loadingMore ? '加载中...' : '加载更多'}}
    </van-button>
  </view>

  <!-- 浮动按钮 -->
  <view class="fab" bindtap="createShift">
    <van-icon name="plus" size="24px" color="#ffffff" />
  </view>
</view>

<!-- 筛选弹窗 -->
<van-popup 
  show="{{showFilterPopup}}" 
  position="bottom" 
  round
  bind:close="closeFilter"
  custom-style="height: 60%;">
  <view class="filter-popup">
    <view class="filter-header">
      <text class="filter-title">筛选条件</text>
      <van-button type="default" size="mini" bindtap="resetFilter">重置</van-button>
    </view>
    
    <view class="filter-content">
      <!-- 状态筛选 -->
      <view class="filter-group">
        <view class="filter-label">交班状态</view>
        <view class="filter-options">
          <van-tag 
            wx:for="{{statusOptions}}" 
            wx:key="value"
            type="{{filterData.status === item.value ? 'primary' : 'default'}}"
            size="medium"
            bindtap="selectStatus"
            data-value="{{item.value}}">
            {{item.text}}
          </van-tag>
        </view>
      </view>

      <!-- 班次类型筛选 -->
      <view class="filter-group">
        <view class="filter-label">班次类型</view>
        <view class="filter-options">
          <van-tag 
            wx:for="{{shiftTypeOptions}}" 
            wx:key="value"
            type="{{filterData.shiftType === item.value ? 'primary' : 'default'}}"
            size="medium"
            bindtap="selectShiftType"
            data-value="{{item.value}}">
            {{item.text}}
          </van-tag>
        </view>
      </view>

      <!-- 优先级筛选 -->
      <view class="filter-group">
        <view class="filter-label">优先级</view>
        <view class="filter-options">
          <van-tag 
            wx:for="{{priorityOptions}}" 
            wx:key="value"
            type="{{filterData.priority === item.value ? 'primary' : 'default'}}"
            size="medium"
            bindtap="selectPriority"
            data-value="{{item.value}}">
            {{item.text}}
          </van-tag>
        </view>
      </view>

      <!-- 时间范围筛选 -->
      <view class="filter-group">
        <view class="filter-label">时间范围</view>
        <van-field
          label="开始日期"
          value="{{filterData.startDate}}"
          placeholder="请选择开始日期"
          readonly
          is-link
          bind:click-input="selectStartDate"
        />
        <van-field
          label="结束日期"
          value="{{filterData.endDate}}"
          placeholder="请选择结束日期"
          readonly
          is-link
          bind:click-input="selectEndDate"
        />
      </view>
    </view>

    <view class="filter-actions">
      <van-button type="default" bindtap="closeFilter">取消</van-button>
      <van-button type="primary" bindtap="applyFilter">确定</van-button>
    </view>
  </view>
</van-popup>

<!-- 二维码弹窗 -->
<van-popup 
  show="{{showQRCodePopup}}" 
  position="center" 
  round
  bind:close="closeQRCode">
  <view class="qrcode-popup">
    <view class="qrcode-header">
      <text class="qrcode-title">交班二维码</text>
      <van-icon name="cross" bindtap="closeQRCode" />
    </view>
    <view class="qrcode-content">
      <image class="qrcode-image" src="{{qrCodeUrl}}" mode="aspectFit"></image>
      <text class="qrcode-tips">请将此二维码发送给接班人员</text>
    </view>
    <view class="qrcode-actions">
      <van-button type="default" bindtap="saveQRCode">保存图片</van-button>
      <van-button type="primary" bindtap="shareQRCode">分享</van-button>
    </view>
  </view>
</van-popup>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Dialog组件 -->
<van-dialog id="van-dialog" />
