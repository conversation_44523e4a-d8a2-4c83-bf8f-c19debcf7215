/* pages/login/login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  display: flex;
  flex-direction: column;
  padding: 0 40rpx;
}

/* 顶部装饰 */
.login-header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0 40rpx;
}

.logo-section {
  text-align: center;
  color: #ffffff;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.app-desc {
  font-size: 28rpx;
  opacity: 0.8;
  line-height: 1.5;
}

/* 登录表单 */
.login-form {
  flex: 2;
  display: flex;
  flex-direction: column;
}

.form-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
  text-align: center;
  margin-bottom: 40rpx;
}

/* 微信登录 */
.login-section {
  margin-bottom: 30rpx;
}

.wx-login-btn {
  width: 100%;
  height: 88rpx !important;
  background: #07c160 !important;
  border-color: #07c160 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.login-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  color: #969799;
  font-size: 24rpx;
  gap: 8rpx;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: #ebedf0;
}

.divider-text {
  padding: 0 20rpx;
  color: #c8c9cc;
  font-size: 24rpx;
}

/* 手机号登录 */
.phone-login-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.code-input-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.send-code-btn {
  width: 160rpx !important;
  height: 64rpx !important;
  font-size: 24rpx !important;
}

.phone-login-btn {
  width: 100%;
  height: 88rpx !important;
  margin-top: 20rpx;
}

/* 用户协议 */
.agreement-section {
  padding: 0 20rpx;
  margin-bottom: 40rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #646566;
  line-height: 1.6;
}

.link-text {
  color: #1989fa;
  text-decoration: underline;
}

/* 底部信息 */
.login-footer {
  text-align: center;
  padding-bottom: 40rpx;
  color: rgba(255, 255, 255, 0.7);
}

.version-info {
  font-size: 24rpx;
  margin-bottom: 16rpx;
}

.support-info {
  font-size: 24rpx;
}

.support-contact {
  color: #ffffff;
  text-decoration: underline;
}

/* 协议弹窗 */
.agreement-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.agreement-content {
  flex: 1;
  padding: 30rpx;
  font-size: 26rpx;
  line-height: 1.8;
  color: #646566;
}

/* 响应式适配 */
@media (max-height: 667px) {
  .login-header {
    padding: 40rpx 0 20rpx;
  }
  
  .logo {
    width: 100rpx;
    height: 100rpx;
  }
  
  .app-name {
    font-size: 40rpx;
  }
}

/* 安全区域适配 */
.login-footer {
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}
