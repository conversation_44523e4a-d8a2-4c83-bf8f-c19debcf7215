// 云函数：获取设备列表
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { department, status, searchKey, page = 1, limit = 50 } = event

  try {
    let query = db.collection('equipments')

    // 构建查询条件
    const whereConditions = {}

    if (department) {
      whereConditions.department = department
    }

    if (status) {
      whereConditions.status = status
    }

    if (searchKey) {
      const searchRegex = new RegExp(searchKey, 'i')
      whereConditions.$or = [
        { name: searchRegex },
        { code: searchRegex },
        { model: searchRegex }
      ]
    }

    if (Object.keys(whereConditions).length > 0) {
      query = query.where(whereConditions)
    }

    // 分页查询
    const skip = (page - 1) * limit
    const result = await query
      .orderBy('department', 'asc')
      .orderBy('name', 'asc')
      .skip(skip)
      .limit(limit)
      .get()

    // 获取总数
    const countResult = await query.count()

    // 格式化设备数据
    const equipments = result.data.map(equipment => ({
      _id: equipment._id,
      name: equipment.name,
      code: equipment.code,
      model: equipment.model,
      department: equipment.department,
      status: equipment.status,
      location: equipment.location,
      purchaseDate: equipment.purchaseDate,
      warrantyDate: equipment.warrantyDate,
      lastMaintenance: equipment.lastMaintenance,
      nextMaintenance: equipment.nextMaintenance,
      specifications: equipment.specifications,
      remark: equipment.remark
    }))

    return {
      success: true,
      data: equipments,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        hasMore: skip + result.data.length < countResult.total
      }
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    return {
      success: false,
      message: '获取设备列表失败',
      error: error.message
    }
  }
}
