// API接口封装
const config = require('./config.js')
const util = require('./util.js')

class ApiService {
  constructor() {
    this.baseUrl = config.api.baseUrl
    this.timeout = config.api.timeout
    this.retryTimes = config.api.retryTimes
  }

  /**
   * 调用云函数
   * @param {string} name 云函数名称
   * @param {object} data 参数
   * @param {boolean} showLoading 是否显示加载
   * @returns {Promise} 请求结果
   */
  async callFunction(name, data = {}, showLoading = true) {
    if (showLoading) {
      util.showLoading('请求中...')
    }

    try {
      const result = await wx.cloud.callFunction({
        name: name,
        data: {
          ...data,
          timestamp: Date.now(),
          userInfo: getApp().globalData.userInfo
        }
      })

      if (showLoading) {
        util.hideLoading()
      }

      if (result.result && result.result.success) {
        return result.result.data
      } else {
        throw new Error(result.result?.message || '请求失败')
      }
    } catch (error) {
      if (showLoading) {
        util.hideLoading()
      }
      console.error(`云函数调用失败 [${name}]:`, error)
      throw error
    }
  }

  /**
   * 数据库查询
   * @param {string} collection 集合名称
   * @param {object} options 查询选项
   * @returns {Promise} 查询结果
   */
  async dbQuery(collection, options = {}) {
    try {
      const db = wx.cloud.database()
      let query = db.collection(collection)

      // 添加查询条件
      if (options.where) {
        query = query.where(options.where)
      }

      // 添加排序
      if (options.orderBy) {
        query = query.orderBy(options.orderBy.field, options.orderBy.order || 'asc')
      }

      // 添加限制
      if (options.limit) {
        query = query.limit(options.limit)
      }

      // 添加跳过
      if (options.skip) {
        query = query.skip(options.skip)
      }

      // 添加字段选择
      if (options.field) {
        query = query.field(options.field)
      }

      const result = await query.get()
      return result.data
    } catch (error) {
      console.error('数据库查询失败:', error)
      throw error
    }
  }

  /**
   * 数据库添加
   * @param {string} collection 集合名称
   * @param {object} data 数据
   * @returns {Promise} 添加结果
   */
  async dbAdd(collection, data) {
    try {
      const db = wx.cloud.database()
      const result = await db.collection(collection).add({
        data: {
          ...data,
          createTime: new Date(),
          updateTime: new Date(),
          creator: getApp().globalData.userInfo?.userId
        }
      })
      return result
    } catch (error) {
      console.error('数据库添加失败:', error)
      throw error
    }
  }

  /**
   * 数据库更新
   * @param {string} collection 集合名称
   * @param {string} id 文档ID
   * @param {object} data 更新数据
   * @returns {Promise} 更新结果
   */
  async dbUpdate(collection, id, data) {
    try {
      const db = wx.cloud.database()
      const result = await db.collection(collection).doc(id).update({
        data: {
          ...data,
          updateTime: new Date(),
          updater: getApp().globalData.userInfo?.userId
        }
      })
      return result
    } catch (error) {
      console.error('数据库更新失败:', error)
      throw error
    }
  }

  /**
   * 数据库删除
   * @param {string} collection 集合名称
   * @param {string} id 文档ID
   * @returns {Promise} 删除结果
   */
  async dbRemove(collection, id) {
    try {
      const db = wx.cloud.database()
      const result = await db.collection(collection).doc(id).remove()
      return result
    } catch (error) {
      console.error('数据库删除失败:', error)
      throw error
    }
  }

  /**
   * 文件上传
   * @param {string} filePath 文件路径
   * @param {string} cloudPath 云端路径
   * @returns {Promise} 上传结果
   */
  async uploadFile(filePath, cloudPath) {
    try {
      util.showLoading('上传中...')
      
      const result = await wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: filePath
      })
      
      util.hideLoading()
      return result
    } catch (error) {
      util.hideLoading()
      console.error('文件上传失败:', error)
      throw error
    }
  }

  /**
   * 文件下载
   * @param {string} fileID 文件ID
   * @returns {Promise} 下载结果
   */
  async downloadFile(fileID) {
    try {
      const result = await wx.cloud.downloadFile({
        fileID: fileID
      })
      return result
    } catch (error) {
      console.error('文件下载失败:', error)
      throw error
    }
  }

  /**
   * 获取临时链接
   * @param {Array} fileList 文件ID列表
   * @returns {Promise} 临时链接结果
   */
  async getTempFileURL(fileList) {
    try {
      const result = await wx.cloud.getTempFileURL({
        fileList: fileList
      })
      return result
    } catch (error) {
      console.error('获取临时链接失败:', error)
      throw error
    }
  }

  // 用户相关API
  async login(code, userInfo) {
    return this.callFunction(config.functions.login, { code, userInfo })
  }

  async getUserInfo(userId) {
    return this.callFunction(config.functions.getUserInfo, { userId })
  }

  // 交班相关API
  async createShift(shiftData) {
    return this.callFunction(config.functions.createShift, shiftData)
  }

  async updateShift(shiftId, updateData) {
    return this.callFunction(config.functions.updateShift, { shiftId, updateData })
  }

  async getShiftList(params = {}) {
    return this.callFunction(config.functions.getShiftList, params)
  }

  async getShiftDetail(shiftId) {
    return this.dbQuery(config.collections.shiftRecords, {
      where: { _id: shiftId }
    }).then(data => data[0])
  }

  // 交接相关API
  async confirmHandover(handoverData) {
    return this.callFunction(config.functions.confirmHandover, handoverData)
  }

  async getHandoverHistory(params = {}) {
    return this.dbQuery(config.collections.handoverLogs, {
      where: params.where || {},
      orderBy: { field: 'createTime', order: 'desc' },
      limit: params.limit || 20,
      skip: params.skip || 0
    })
  }

  // 消息相关API
  async sendNotification(notificationData) {
    return this.callFunction(config.functions.sendNotification, notificationData)
  }

  async getNotifications(params = {}) {
    return this.dbQuery(config.collections.notifications, {
      where: params.where || {},
      orderBy: { field: 'createTime', order: 'desc' },
      limit: params.limit || 20,
      skip: params.skip || 0
    })
  }

  async markNotificationRead(notificationId) {
    return this.dbUpdate(config.collections.notifications, notificationId, {
      isRead: true,
      readTime: new Date()
    })
  }

  // 统计相关API
  async getStatistics(params = {}) {
    return this.callFunction(config.functions.getStatistics, params)
  }

  // 器械相关API
  async getInstruments(category) {
    const cacheKey = `${config.cache.instrumentList}_${category}`
    let instruments = util.getStorage(cacheKey)
    
    if (!instruments) {
      instruments = await this.dbQuery(config.collections.instruments, {
        where: category ? { category } : {},
        orderBy: { field: 'name', order: 'asc' }
      })
      util.setStorage(cacheKey, instruments, config.cache.expireTime)
    }
    
    return instruments
  }

  // 部门相关API
  async getDepartments() {
    const cacheKey = config.cache.departmentList
    let departments = util.getStorage(cacheKey)
    
    if (!departments) {
      departments = await this.dbQuery(config.collections.departments, {
        orderBy: { field: 'name', order: 'asc' }
      })
      util.setStorage(cacheKey, departments, config.cache.expireTime)
    }
    
    return departments
  }
}

// 创建API实例
const api = new ApiService()

module.exports = api
